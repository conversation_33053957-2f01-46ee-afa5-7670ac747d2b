function devMode(){	
	const devModeButton = document.querySelector('[data-name="dev-mode-button"]');
	const devModeStatus = document.querySelector('[data-name="dev-mode-status"]');

	function isDevMode() {
		return localStorage.getItem('twk-dev-mode') === 'true';
	}
	function getDevMode() {
		if ( ! devModeStatus ) return;

		if ( isDevMode() ) {
			devModeStatus.innerText = 'ON';
		} else {
			devModeStatus.innerText = 'OFF';
		}
	}

	async function activateDevMode(reload = false) {
		localStorage.setItem('twk-dev-mode', 'true');
		devModeStatus ? devModeStatus.innerText = 'ON' : '';
	
		// Wait for CSS to load
		await new Promise(resolve => {
			const stylesheet = loadCSS('dev');
			stylesheet.addEventListener('load', resolve);
		});
	
		// Reload after everything is done
		if ( reload ) {
			window.location.reload();
		}
	}

	async function deactivateDevMode(reload = false){
		localStorage.setItem('twk-dev-mode', 'false');
		devModeStatus ? devModeStatus.innerText = 'OFF' : '';
	
		await new Promise(resolve => {
			const stylesheet = loadCSS('prod');
			stylesheet.addEventListener('load', resolve);
		});
	
		// Reload after everything is done
		if ( reload ) {
			window.location.reload();
		}
	}
	
	/**
	 * Switches between development and production CSS files
	 * @param {'dev' | 'prod'} mode - The mode to switch to
	 * @throws {Error} If invalid mode
	 */
	function loadCSS(mode) {
		// Configuration
		const config = {
			dev: {
				oldId: 'screen-css-css',
				newId: 'screen-dev-css-css',
				oldFile: 'styles.css',
				newFile: 'styles--dev.css'
			},
			prod: {
				oldId: 'screen-dev-css-css',
				newId: 'screen-css-css',
				oldFile: 'styles--dev.css',
				newFile: 'styles.css'
			}
		};
	
		// Validate mode
		if (!['dev', 'prod'].includes(mode)) {
			throw new Error('Invalid mode. Use "dev" or "prod"');
		}
	
		const settings = config[mode];
		const oldStylesheet = document.querySelector(`#${settings.oldId}`);
		const newStylesheet = document.querySelector(`#${settings.newId}`);
	
		// If already in requested mode, do nothing
		if (newStylesheet?.href) {
			return newStylesheet;
		}
	
			// Create new stylesheet
		const stylesheet = document.createElement('link');
		stylesheet.id = settings.newId;
		stylesheet.rel = 'stylesheet';
	
		// Set URL based on existing stylesheet or root path
		const baseURL = oldStylesheet?.href 
			? oldStylesheet.href.replace(settings.oldFile, settings.newFile)
			: document.querySelector('#screen-css-css').href.replace('styles.css', settings.newFile);
	
		stylesheet.href = baseURL;
	
		// Remove old stylesheet after new one loads
		stylesheet.onload = () => {
			oldStylesheet?.remove();
		};
	
		document.head.appendChild(stylesheet);
		return stylesheet;
	}


	// On load
	getDevMode()
	if ( isDevMode() ) {
		activateDevMode()
	} else {
		deactivateDevMode()
	}

	// On user interaction
	devModeButton?.addEventListener('click', function(){
		if ( isDevMode() ) {
			deactivateDevMode(true)
		} else {
			activateDevMode(true)
		}
	})
}
export default devMode;