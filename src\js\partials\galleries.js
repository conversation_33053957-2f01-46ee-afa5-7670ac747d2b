import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";

// GALLERY IMAGE
function wpGallery(){
    document.querySelectorAll('.gallery[class*="gallery-columns-"]').forEach((gallery, index) => {
		const galleryLinks = gallery.querySelectorAll('a');

		if (galleryLinks.length) {
			// Add a unique data-fancybox attribute per gallery
			galleryLinks.forEach(link => link.setAttribute('data-fancybox', `wp-gallery-${index}`));  // For the popup to work, all the links need to have the same data-fancybox attribute.

			// Bind Fancybox to these links
			Fancybox.bind(galleryLinks, {
				mainClass: 'fancybox-wp-gallery',
				Carousel: {
					friction: 0.8,
					preload: 2,
					infinite: true,
					Toolbar: true, // https://fancyapps.com/fancybox/guides/toolbar/
					Thumbs: true,  // https://fancyapps.com/fancybox/guides/thumbnails/
				},
				caption: el => el.closest('figure')?.querySelector('figcaption')?.innerHTML || ''
			});
		}
	});

}

export { wpGallery };
