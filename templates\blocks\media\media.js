import gsap from "gsap";

function getScale(element) {
    // Get the computed style of the element
    const style = window.getComputedStyle(element);
    // Get the transform matrix from the style
    const transform = style.transform || style.webkitTransform || style.mozTransform;
    // If there is no transform, return the default scale of 1
    if (!transform || transform === 'none') {
        return 1;
    }
    // Parse the matrix values
    const matrix = transform.match(/^matrix\((.+)\)$/);
    if (matrix) {
        // Split the matrix values and return the scale (a = matrix[1])
        const values = matrix[1].split(', ');
        const scaleX = parseFloat(values[0]);
        // Assuming uniform scaling, you can return scaleX
        return scaleX;
    }
    // Parse the matrix3d values
    const matrix3d = transform.match(/^matrix3d\((.+)\)$/);
    if (matrix3d) {
        // Split the matrix3d values and return the scale (a = matrix3d[0])
        const values = matrix3d[1].split(', ');
        const scaleX = parseFloat(values[0]);
        // Assuming uniform scaling, you can return scaleX
        return scaleX;
    }
    // If no valid transform matrix is found, return the default scale of 1
    return 1;
}

function cursor() {	
    if ( ('ontouchstart' in window) || (navigator.maxTouchPoints > 0) || (navigator.msMaxTouchPoints > 0) ) return; // If touch device, return
    
	const videos = document.querySelectorAll('[data-name="video"]');

	if ( videos.length > 0 ) {
		videos.forEach(video => {
			const videoCursor = video.querySelector('[data-name="video-cursor"]');
			const videoSlideParent = video.closest('[data-name="video"]');
			let insideVideo = false;
			let lastMouseX = 0;
			let lastMouseY = 0;
	
			if ( videoCursor ) {
				gsap.set(videoCursor, {xPercent: -50, yPercent: -50});
	
				let xTo = gsap.quickTo(videoCursor, 'x', {duration: 0.3, ease: 'power2.out', force3D: true});
				let yTo = gsap.quickTo(videoCursor, 'y', {duration: 0.3, ease: 'power2.out', force3D: true});
	
				const updateCursorPosition = () => {
					if (insideVideo) {
						const scale = videoSlideParent ? getScale(videoSlideParent) : 1; // Ensure this function returns the correct scale
						const rect = video.getBoundingClientRect();
	
						const mouseX = (lastMouseX - rect.left) / scale;
						const mouseY = (lastMouseY - rect.top) / scale;
	
						xTo(mouseX - (video.clientWidth / 2));
						yTo(mouseY - (video.clientHeight / 2));
					} else {
						xTo(0);
						yTo(0);
					}
				};
	
				window.addEventListener('mousemove', e => {
					lastMouseX = e.clientX;
					lastMouseY = e.clientY;
					updateCursorPosition();
				});
	
				window.addEventListener('scroll', updateCursorPosition);
			}
	
			video.addEventListener('mouseenter', () => {
				insideVideo = true;
			});
		
			video.addEventListener('mouseleave', () => {
				insideVideo = false;
			});

			// To avoid the play button to keep moving with you while you scroll.
			window.addEventListener('scroll', () => {				
				insideVideo = false;
			});
		});
	}
}

function media(){
	cursor();
}

export default media;