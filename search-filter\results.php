<?php
/**
 * Sample Results Template
 *
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs
 * and using template tags.
 *
 * http://codex.wordpress.org/Template_Tags
 *
 * @package ISZL
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $query ) ) {
	return;
}

if ( $query->have_posts() ) {
	$paged = isset( $query->query['paged'] ) ? $query->query['paged'] : 1;
	?>
	<!-- Keep the `.search-filter-query-posts` class to support the load more button -->
	<div class="container">
		<div class="search-filter-query-posts">
			<div class="grid grid-cols-12 gap-15">
				<?php
				while ( $query->have_posts() ) {
					$query->the_post();
					?>
					<div class="col-span-4">
						<?php require locate_template( 'templates/parts/teaser.php' ); ?>
					</div>
					<?php
				}
				wp_reset_postdata();
				?>
			</div>

		</div>
	</div>
	
	<?php require locate_template( 'templates/parts/_pagination.php' ); ?>
	<?php
} else {
	?>
	<div class="container">
		<div class="grid grid-cols-12 gap-15">
			<div class="col-span-12 text-center py-40">
				<h2 class="mb-20">No Results Found</h2>
				<p>Please try adjusting your search criteria.</p>
			</div>
		</div>
	</div>
	<?php
}
?>
