<?php
/**
 * Template part to display the flexible content.
 *
 * @package twkmedia
 */

if ( post_password_required() ) :
	?>

	<div class="container my-50">
		<div class="flex flex-wrap justify-center">
			<div class="w-full md:w-10/12 2xl:w-8/10">
				<div class="gform_wrapper">
					<?php echo get_the_password_form(); ?>
				</div>
			</div>
		</div>
	</div>

<?php else :
	$blocks = get_field( 'blocks' );

	// check if the flexible content field has rows of data.
	if ( have_rows( 'blocks' ) ) :
		// loop through the rows of data.
		$block_number = 0;
		while ( have_rows( 'blocks' ) ) :
			the_row();
			$block_number++;

			$layout = get_row_layout();
			$layout = str_replace( '_', '-', $layout );

			// Get next and prev blocks if any.
			$current_row_index = intval( get_row_index() );
			$next_block = ( isset( $blocks[ $current_row_index ] ) ? $blocks[ $current_row_index ] : null );
			$prev_block = ( isset( $blocks[ $current_row_index - 2 ] ) ? $blocks[ $current_row_index - 2 ] : null );

			if ( $next_block ) {
				$next_layout = $next_block['acf_fc_layout'];
				$next_layout = str_replace( '_', '-', $next_layout );
			} else {
				$next_layout = null;
			}

			if ( $prev_block ) {
				$prev_layout = $prev_block['acf_fc_layout'];
				$prev_layout = str_replace( '_', '-', $prev_layout );
			} else {
				$prev_layout = null;
			}

			$spacing = 'my-40 md:my-80'; // NOTE: Add here the block margins. Use conditionals in case one block ($layout) has different margins.

			if ( $block_number === 1 ) {
				$spacing = 'mt-30 mb-40 mt-70 md:mb-80';
			}
			?>

			<section
				id="<?php echo ( get_sub_field( 'twk_settings_block_custom_id' ) ? esc_attr( get_sub_field( 'twk_settings_block_custom_id' ) ) : esc_attr( 'block-' . $block_number ) ); ?>"
				class="block block--<?php echo $layout; ?> <?php echo $spacing; ?>"
				data-block="<?php echo $layout; ?>"
				<?php echo ( $next_block ? 'data-next-block=' . $next_layout : '' ); ?>
				<?php echo ( $prev_block ? 'data-prev-block=' . $prev_layout : '' ); ?>
				data-block-number="<?php echo $block_number; ?>"
				data-block-title="<?php echo get_sub_field( 'twk_settings_easy_identifier' ); ?>"
				>

				<?php include locate_template( 'templates/blocks/' . $layout . '/' . $layout . '.php' ); ?>

			</section>

			<?php
		endwhile;

	endif;
endif;
