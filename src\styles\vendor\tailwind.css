@source inline("scroll-lock"); /* Previous safelist */

@theme {
	/* fonts */
	--font-sans: "nitti-grotesk", sans-serif;
	--font-serif: "nitti-grotesk-condensed", sans-serif;
	--font-nitti-grotesk: "nitti-grotesk", sans-serif;
	--font-nitti-grotesk-condensed: "nitti-grotesk-condensed", sans-serif;
	/* colors */
	--color-pure-black: #000;
	--color-black: #072252;
	--color-white: #fff;
	--color-transparent: transparent;
	--color-blue: #0F4DBC;
	--color-blue-dark: #00389D;
	--color-yellow: #EBB700;
	--color-gradient-start: #B71234;
	--color-gradient-end: #5A3188;
	/* spacing */
	--spacing: 1px;	
	/* breakpoints */
	--breakpoint-*: initial;
	--breakpoint-xs: 340px;
	--breakpoint-sm: 576px;
	--breakpoint-md: 768px;
	--breakpoint-lg: 992px;
	--breakpoint-xl: 1280px;
	--breakpoint-2xl: 1536px;
	--breakpoint-3xl: 1920px;
	/* radius */
	--radius-0: 0px;
	--radius-full: 9999px;
}

@custom-variant letterbox (@media (max-height: 750px) and (min-width: 900px));
/* @custom-variant landscape (@media (max-height: 780px) and (min-width: 900px)); */


.container{
	@apply mx-auto px-30 sm:px-40 max-sm:max-w-none;
}
