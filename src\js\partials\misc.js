/**
 * Open PDFs in new window
 */
function linksCustomization(){
	var pdfLinks = document.querySelectorAll('a[href$=".pdf"]')

	pdfLinks.forEach(pdf => {
		pdf.setAttribute('target', '_blank');
	});
}

/**
 * Adds smooth scrolling to anchor links
 * 
 * Adds data-scroll-to attribute to anchor links without it.
 */
function scrollTo(lenis){
	// Function to handle smooth scrolling for anchor links
	function smoothScroll(targetSelector, offset = 0) {
		const element = document.querySelector(targetSelector);

		if (element) {
			// Get the target scroll position
			const targetY = element.getBoundingClientRect().top + window.scrollY - offset;

			// Use Lenis to scroll
			lenis.scrollTo(targetY, {
				duration: 1.2,   // seconds
				easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)) // match your Lenis easing
			});
		}
	}

	// Detect all anchor links without your special attributes
	const scrollToLinks = document.querySelectorAll('a[href^="#"]:not([data-scroll]):not([class^="magnific-"]):not([data-fancybox])');
	scrollToLinks.forEach(link => {
		if (link.getAttribute('href').length > 1) {
		link.setAttribute('data-scroll', 'scroll-to');
		}
	});

	// Event listener for anchor links
	const anchorLinks = document.querySelectorAll('[data-scroll="scroll-to"]');
	anchorLinks.forEach(element => {
		element.addEventListener('click', function(event) {
			event.preventDefault();

			const target = event.currentTarget;

			let offset = 50; // Default offset
			if (target.getAttribute('data-scroll-space')) {
				offset = parseInt(target.getAttribute('data-scroll-space'), 10);
			}

			smoothScroll(target.getAttribute('href'), offset);
		});
	});
}


/**
 * Adds a class to tablepress tables to make them responsive.
 */
function responsiveTables(){
	if ( document.querySelectorAll('.prose table').length > 0 ) {
		const tables = document.querySelectorAll('.prose table');

		var wrap = function (toWrap, wrapper) {
			wrapper = wrapper || document.createElement('div');
			wrapper.classList.add('responsive-table');
			
			// Insert the wrapper before the toWrap element
			toWrap.parentNode.insertBefore(wrapper, toWrap);
			
			return wrapper.appendChild(toWrap);
		};

		[...tables].forEach((table) => {
			wrap(table);
		});
	}
}

export { linksCustomization, scrollTo, responsiveTables }