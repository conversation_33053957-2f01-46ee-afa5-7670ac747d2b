body{
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	@apply text-black overflow-x-clip;
}

#page-wrap{
	@apply flex flex-col min-h-screen;
}
#content-wrap{
	@apply flex-1;
}

[data-hidden-on-load]{
	opacity: 0;
}


/* Lenis */
html.lenis, html.lenis body {
	height: auto;
}
.lenis.lenis-smooth {
	scroll-behavior: auto !important;
}
.lenis.lenis-smooth [data-lenis-prevent] {
	overscroll-behavior: contain;
}
.lenis.lenis-stopped {
	overflow: hidden;
}
.lenis.lenis-smooth iframe {
	pointer-events: none;
}




/* Scroll Animations - starting point */

/* JS available and motion OK */
@media (scripting: enabled) and (prefers-reduced-motion: no-preference) {
	[twk-aos] {
		@apply opacity-0;
	}
}

/* JS disabled or reduced motion enabled */
/* @media (scripting: none), (prefers-reduced-motion) {} */


/* END - Scroll Animations - starting point */
