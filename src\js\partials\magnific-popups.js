import "magnific-popup";

// IMPORTANT: External link will be open in a new tab, and "video popups" count as "external".
// To prevent this, "magnific-video" should be the first class and with no empty spaces. 
// ex. <a href="url" class="magnific-video other-classes">


// IFRAME
function magnificPopupIframe(){
	$('.magnific-video').magnificPopup({
		type: 'iframe',
		mainClass: 'mfp-video-wrapper',
		removalDelay: 160,
		preloader: false,
		fixedContentPos: false,
	
		iframe: {
			markup: '<div class="mfp-iframe-scaler">' +
				'<div class="mfp-close"></div>' +
				'<iframe class="mfp-iframe" frameborder="0" allow="autoplay"></iframe>' +
				'</div>',
			patterns: {
				youtu: {
					index: 'youtu.be',
					id: function( url ) {
					
						// Capture everything after the hostname, excluding possible querystrings.
						var m = url.match( /^.+youtu.be\/([^?]+)/ );
				
						if ( null !== m ) {
							return m[1];
						}
				
						return null;
			
					},
					// Use the captured video ID in an embed URL. 
					// Add/remove querystrings as desired.
					src: '//www.youtube-nocookie.com/embed/%id%?autoplay=1&rel=0'
				},
				youtube: {
					index: 'youtube.com/',
					id: 'v=',
					src: 'https://www.youtube-nocookie.com/embed/%id%?autoplay=1'
				},
				vimeo: {
					index: 'vimeo.com/',
					id: function(url) {        
						var m = url.match(/(https?:\/\/)?(www.)?(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/);
						if ( !m || !m[5] ) return null;
						return m[5];
					},
					src: 'https://player.vimeo.com/video/%id%?autoplay=1&dnt=1'
				}
			}
		}
	});
}

// INLINE
function magnificPopupInline(){
	$('.magnific-inline-popup').magnificPopup({
		type: 'inline',
	
		fixedContentPos: false,
		fixedBgPos: true,
	
		overflowY: 'auto',
	
		closeBtnInside: true,
		preloader: false,
		
		midClick: true,
		removalDelay: 300,
		mainClass: 'my-mfp-zoom-in'
	});
}

// GALLERY SINGLE IMAGE
function magnificPopupSingleImage(){
	$('.magnific-gallery-single-image').magnificPopup({
		type: 'image',
		closeOnContentClick: true,
		mainClass: 'mfp-img-single',
		image: {
			verticalFit: true,
			titleSrc: function titleSrc(item) {
			return item.el.attr('title');
			}
		},
		gallery: {
			enabled: false
		}
	});          
}

// GALLERY IMAGE
function magnificPopupGalleryImage(){
	$('.magnific-gallery-image').magnificPopup({
		type: 'image',
		closeOnContentClick: true,
		mainClass: 'mfp-img-mobile',
		image: {
			verticalFit: true,
			titleSrc: function(item) {
				return item.el.attr('title');
			}
		},
		gallery:{
			enabled:true
		}
	});
}

function magnificPopup(){
	magnificPopupIframe();
	magnificPopupInline();
	magnificPopupSingleImage();
	magnificPopupGalleryImage();
}

export default magnificPopup;
