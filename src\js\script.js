import devMode from './dev-mode';
import { lenisSetup } from './partials/lenis';
import columnBreak from './utils/column-break';
import { linksCustomization, scrollTo, responsiveTables } from './partials/misc';
//import scrollAnimations from './vendor/scrollAnimations-v2';
import popups from './partials/popups'
import { basicSlider } from './partials/sliders';
import { video } from './partials/video';
import { wpGallery } from './partials/galleries';

import notifications from '../../templates/sections/notifications/notifications';
import { bannerSkipLinks } from './partials/banner';
import { bannerVideo } from '../../templates/sections/banner/banner-video';

/* Flexible Content */
import accordion from '../../templates/blocks/accordion';
import tabs from '../../templates/blocks/tabs/tabs';
import textAndMedia from '../../templates/blocks/text-and-media/text-and-media';
import media from '../../templates/blocks/media/media';


function consoleMessage(){	
	console.log('%cCreated by %cTWK%cwww.thewebkitchen.co.uk', 'background: #13212E; color: #FFFFFF; padding: 5px 0px 5px 10px; margin: 25px 0px;', 'background: #13212E; color: #05E5C8; padding: 5px 10px 5px 0px; font-weight: bold;', 'background: #FFFFFF; padding: 5px 10px;');
}

function usingJS(){
	document.querySelector('html').classList.remove('no-js')
	document.querySelector('html').classList.add('js')
}



/* *********************** */
/* Start all the functions */
/* *********************** */
window.onload = function() {
	document.querySelectorAll( '[data-hidden-on-load]' )?.forEach(function(el){
		el.removeAttribute('data-hidden-on-load')
	})
	
	// Init
	consoleMessage()
	usingJS()
	devMode()
	columnBreak()
	bannerSkipLinks()
	linksCustomization()
	const lenis = lenisSetup(); // get the Lenis instance
	scrollTo(lenis)
	responsiveTables()
	video()
	wpGallery()
	//scrollAnimations()
	notifications()
	bannerVideo()

	// Popups
	popups()

	// Sliders
	basicSlider()

	// Flexible Content
	accordion()
	tabs()
	textAndMedia()
	media()
}

