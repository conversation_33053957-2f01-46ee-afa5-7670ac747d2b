function bannerSkipLinks(){
	const blocks         = document.querySelectorAll('[data-block]');
	const customSections = document.querySelectorAll('[data-custom-section]');
	const allSections = Array.from(blocks).concat(Array.from(customSections));

	if ( allSections.length === 0 ) return;

	// Create array of block data (only include blocks with titles)
	const blockData = Array.from(allSections).map(block => ({
		id: block.id,
		title: block.dataset.blockTitle,
	})).filter(item => item.title);

	const skipLinksBlock = document.querySelector('[data-name="banner-skip-links"]');
	const skipLinksList  = document.querySelector('[data-name="banner-skip-links"] ul');
	
	if (  skipLinksBlock === null ) return;
	if (  blockData.length === 0 ) {
		skipLinksBlock.classList.add('hidden'); // NO links, hide the block
		return;
	}

	// Create the navigation list
	blockData.forEach(block => {
		const li = document.createElement('li')
		const link = document.createElement('a')

		link.classList.add('text-20ncb', 'text-white', 'hover:text-yellow', 'transition-color', 'duration-300')
		link.href = `#${block.id}`;
		link.setAttribute('data-scroll', 'scroll-to');
		link.innerHTML = `${block.title}`;
		
		li.appendChild(link);
		skipLinksList.appendChild(li);
	});
}

export { bannerSkipLinks };
