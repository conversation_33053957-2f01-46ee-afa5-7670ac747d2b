document.addEventListener("DOMContentLoaded", function() {
	let scriptToLoad = php_vars.themeDirUrl + '/assets/js/bundle.js';

	if ( localStorage.getItem('twk-dev-mode') === 'true') {
		scriptToLoad = php_vars.themeDirUrl + '/assets/js/bundle--dev.js';
	}

	// Dynamically load the JS file
	const scriptElement = document.createElement('script');
	scriptElement.src = scriptToLoad;
	scriptElement.type = 'text/javascript';
	document.head.appendChild(scriptElement);
});
