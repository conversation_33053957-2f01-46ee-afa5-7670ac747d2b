{"$schema": "https://aka.ms/codetour-schema", "title": "Instagram Feed", "steps": [{"file": "functions.php", "description": "First step is to include these two files. Uncomment the lines.\n\n**require_once 'lib/instagram-api-class.php';**\n\n**require_once 'lib/instagram-api-call.php';**", "line": 18}, {"file": "lib/instagram-api-call.php", "description": "You will need an Instagram token. If you have access to the account, you can generate it on this URL.\n\nAsk the PM/AM to get the token if not.", "line": 4}, {"file": "lib/instagram-api-call.php", "description": "Copy this code and add it where you want the Instgram feed to show.", "line": 8}, {"file": "lib/instagram-api-call.php", "description": "If you need more than one Instagram account, you can add them here. If not, leave it as it is.", "line": 76}], "ref": "main"}