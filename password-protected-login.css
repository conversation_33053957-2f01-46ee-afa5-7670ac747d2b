.login .wp-login-logo a {
	background-image: url('assets/img/twk-logo-white.svg');
	padding-bottom: 0px;
	height: 50px;
	width: 300px;
	background-size: 250px;
	margin-bottom: 0;
}

.login .button-primary {
	background-color: #00A995;
	color: black;
	text-transform: uppercase;
	text-shadow: none;
	box-shadow: none;
	transition: all .3s ease-in;
}

.login .button-primary:hover,
.login .button-primary:focus {
	color: white;
	background-color: #0C161F;
}

body.login {
	background: #14222f;
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-position: center;
	background-size: cover;
}

.login #backtoblog a,
.login #nav a,
.login a.privacy-policy-link{
	color: white;
	text-decoration: none;
}

.login form,
.login #login_error {
	border-radius: 0;
}

.login form .input,
.login form input[type=checkbox],
.login input[type=text] {
	border-radius: 0;
}

.wp-core-ui .button-group.button-large .button,
.wp-core-ui .button.button-large {
	padding: 5px 20px;
	height: auto;
	border: none;
	border-radius: 0;
}

#wp-auth-check-wrap #wp-auth-check {
	background-color: black;
}