const fs = require('fs');
const path = require('path');

const mainFile = path.resolve(__dirname, 'src/styles/editor-styles.css');
const proseFile = path.resolve(__dirname, 'src/styles/partials/prose.css');
const buttonsFile = path.resolve(__dirname, 'src/styles/partials/buttons.css');
const tempFile = path.resolve(__dirname, 'src/styles/editor-styles--bundle.css');

const inputMain = fs.readFileSync(mainFile, 'utf8');
const inputProse = fs.readFileSync(proseFile, 'utf8');
const inputButtons = fs.readFileSync(buttonsFile, 'utf8');


function processProseContent(content) {
    return content.replace(/\.prose/g, '#tinymce');
}
function processButtonsContent(content) {
    return content.replace(/\.button/g, '#tinymce .button');
}

const imports = "@import 'tailwindcss/utilities'; @import 'partials/typography';"

const processedProseContent = processProseContent(inputProse);
const processedButtonsContent = processButtonsContent(inputButtons);
const finalFileContent = imports + processedProseContent + processedButtonsContent + inputMain;

fs.writeFileSync(tempFile, finalFileContent);

console.log('Editor styles file created successfully.');
