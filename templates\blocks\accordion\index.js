import gsap from 'gsap';

function closeAllAccordions(accordions, clickedAccordion) {
    accordions.forEach((accordion) => {
        if ( accordion === clickedAccordion ) return; // skip the clicked accordion

        const content = accordion.querySelector('[data-target="accordion-content"]');
        accordion.dataset.state = 'closed';
        gsap.to(content, { height: 0, duration: 0.5 });
		gsap.to(accordion.querySelector('.icon .plus-vertical'), { rotate: '0deg', transformOrigin: 'center', duration: 0.5 });
    });
}


function accordion() {
    const accordions =  document.querySelectorAll('[data-target="accordion"]');

    if ( ! accordions ) return;

    accordions.forEach((accordion) => {
        const trigger = accordion.querySelector('[data-target="accordion-trigger"]');
        const content = accordion.querySelector('[data-target="accordion-content"]');

        trigger.addEventListener('click', (event) => {
            const { state } = accordion.dataset;

            closeAllAccordions(accordions, event.currentTarget.parentNode);

			if ( state === 'closed' ) {
				accordion.dataset.state = 'open';
				gsap.to(content, { height: 'auto', duration: 0.5 });
				gsap.to(accordion.querySelector('.icon .plus-vertical'), { rotate: '-90deg', transformOrigin: 'center', duration: 0.5 });
			} else {
				accordion.dataset.state = 'closed';
				gsap.to(content, { height: 0, duration: 0.5 });
				gsap.to(accordion.querySelector('.icon .plus-vertical'), { rotate: '0deg', transformOrigin: 'center', duration: 0.5 });
			}
        });
    });
}

export default accordion;