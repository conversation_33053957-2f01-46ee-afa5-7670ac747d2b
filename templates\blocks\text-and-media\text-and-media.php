<?php
/**
 * Template part to display the block - Text and Media
 *
 * @package twkmedia
 */

$initial_img_position = get_sub_field( 'twk_settings_initial_image_position' );
$alternate_position   = get_sub_field( 'twk_settings_alternate_position' );
?>

<div class="container space-y-40 md:space-y-50 xl:space-y-80">

	<?php
	if ( have_rows( 'content' ) ) :
		$row_number = -1;

		while ( have_rows( 'content' ) ) :
			the_row();
			$row_number++;

			$img_right  = 'order-1 md:order-2';
			$img_left   = 'order-1';
			$text_right = 'order-2';
			$text_left  = 'order-2 md:order-1';

			// If set to not alternate position.
			if ( ! $alternate_position ) {
				// If image position is set to left.
				if ( $initial_img_position == 'left' ) {
					$text_position  = $text_right;
					$image_position = $img_left;
				} else { // If image position is set to right.
					$text_position = $text_left;
					$image_position = $img_right;
				}
			} else {  // If set to alternate position.
				// If image position is set to left.
				if ( $initial_img_position == 'left' ) {
					// If row is even.
					if ( $row_number % 2 === 0 ) {
						$text_position = $text_right;
						$image_position = $img_left;
					} else { // If row is odd.
						$text_position = $text_left;
						$image_position = $img_right;
					}
				} else { // If image position is set to right.
					// If row is even.
					if ( $row_number % 2 === 0 ) {
						$text_position = $text_left;
						$image_position = $img_right;
					} else { // If row is odd.
						$text_position = $text_right;
						$image_position = $img_left;
					}
				}
			}

			$video_title           = get_sub_field( 'full_video_title' );
			$video_url             = get_sub_field( 'full_video_vimeo_url' );
			$video_background_type = get_sub_field( 'background_video_type' );
			$video_background      = get_sub_field( 'background_video_from_media_library' );
			$vimeo_background      = get_sub_field( 'background_video_vimeo_id' );
			$adaptive_background   = get_sub_field( 'background_adaptive_video' );
			$cover_image           = get_sub_field( 'image' );
			?>


			<div class="grid grid-cols-12 gap-y-30 md:gap-x-50 xl:gap-x-70 w-full">
				<div class="col-span-12 md:col-span-6 flex <?php echo $image_position; ?>">
		
					<div class="group w-full h-full overflow-hidden max-h-[80dvh] sticky top-[10dvh] max-lg:min-h-[50dvh]">
						<?php if ( $video_background_type == 'file' && $video_background ) : ?>
							<video
								data-name="video-lazy-load"
								poster="<?php echo ( isset( $cover_image['url'] ) ? $cover_image['url'] : '' ); ?>"
								autoplay muted loop playsinline preload="none"
								class="fill-image opacity-0 transition-opacity duration-500"
								oncanplay="this.classList.remove('opacity-0')">
								<source data-src="<?php echo $video_background['url']; ?>" src="" type="<?php echo $video_background['mime_type']; ?>">
								Your browser does not support HTML5 video.
							</video>
						<?php elseif ( $video_background_type == 'adaptive' && $adaptive_background ) : ?>
							<video
								data-name="adaptive-video"
								data-source="<?php echo $adaptive_background; ?>"
								poster="<?php echo ( isset( $cover_image['url'] ) ? $cover_image['url'] : '' ); ?>"
								autoplay muted loop playsinline
								class="fill-image"
								>
							</video>
						<?php elseif ( $video_background_type == 'vimeo' && $vimeo_background ) : ?>
							<div class="absolute w-full h-full overflow-hidden">
								<iframe
									data-name="iframe-video"
									class="absolute z-0 top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 min-w-[60vw] min-h-[60vh] object-cover pointer-events-none"
									src="https://player.vimeo.com/video/<?php echo $vimeo_background; ?>?autoplay=1&amp;loop=1&amp;title=0&amp;byline=0&amp;portrait=0&amp;background=1&amp;controls=0&amp;muted=1&amp;playsinline=1&amp;autopause=0"
									frameborder="0" allow="autoplay; fullscreen" allowfullscreen=""
									title="<?php echo $video_title; ?>" data-ready="true"></iframe>
							</div>
						<?php else : ?>
							<div class="relative w-full h-full overflow-hidden bg-black/10">
								<?php echo twk_output_acf_image( $cover_image, 'large', 'fill-image' ); ?>
							</div>
						<?php endif; ?>

						<?php if ( $video_url ) : ?>
							<a href="<?php echo $video_url; ?>" data-fancybox>
								<img
									src="<?php echo get_template_directory_uri(); ?>/assets/img/icons/play.svg" alt="play icon"
									class="absolute z-30 top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 size-80 group-hover:scale-125 ease-in-out transition-all duration-300">
							</a>
						<?php endif; ?>

						<?php if ( $video_title ) : ?>
							<div class="absolute z-20 bottom-20 left-30 right-30">
								<h3 class="text-white"><?php echo $video_title; ?></h3>
							</div>
						<?php endif; ?>
						<!-- Gradient -->
						<div class="absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 via-25% pointer-events-none z-10"></div>
					</div>

				</div>
				<div class="col-span-12 md:col-span-6 md:py-30 flex flex-col <?php echo $text_position; ?>">
					<div class="prose">
						<?php the_sub_field('text'); ?>
					</div>
				</div>
			</div>
		<?php endwhile; ?>
	<?php endif; ?>
</div>
