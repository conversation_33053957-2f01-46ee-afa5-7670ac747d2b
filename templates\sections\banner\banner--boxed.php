<?php
/**
 * Template part for displaying the page banner media image.
 *
 * @package twkmedia
 */

$image_url = twk_get_image_url_with_fallback( $post->ID, 'full' );
?>

<section data-name="banner-media-image">
	<div class="grid grid-cols-12">
		<div class="col-span-2">
			<?php
			$ancestors = get_post_ancestors($post);

			if ( $ancestors ) :
				$top_ancestor_id    = $ancestors[count($ancestors) - 1] ? $ancestors[count($ancestors) - 1] : $ancestors[0];
				$top_ancestor_title = get_the_title($top_ancestor_id);
				?>
				<h2 class="text-20ncb -rotate-90 origin-center mt-345">
					<?php echo $top_ancestor_title; ?>
				</h2>
				<?php
			endif;
			?>
			

		</div>
		<div class="col-span-10">
			<div class="relative aspect-[1230/600]">
				<?php if ( get_field( 'twk_settings_banner_media_type' ) === 'video' ) : ?>
					<?php
					$poster = get_field( 'banner_image' );

					if ( get_field( 'twk_settings_video_type' ) === 'file' ) {
						if ( get_field( 'banner_video_desktop' ) ) {
							$video_desktop_url  = get_field( 'banner_video_desktop' )['url'];
							$video_desktop_mime = get_field( 'banner_video_desktop' )['mime_type'];
						}
						if ( get_field( 'banner_video_mobile' ) ) {
							$video_mobile_url  = get_field( 'banner_video_mobile' )['url'];
							$video_mobile_mime = get_field( 'banner_video_mobile' )['mime_type'];
						}
					} elseif ( get_field( 'twk_settings_video_type' ) === 'url' ) {
						$video_url  = get_field( 'banner_video_url' );
						$video_mime = 'video/mp4';
					}
					?>
					<?php if ( get_field( 'twk_settings_video_type' ) === 'file' ) : ?>
						<!-- Desktop and mobile Video -->
						<video
							poster="<?php echo ( isset( $poster['url'] ) ? $poster['url'] : '' ); ?>"
							preload="none" muted loop playsinline
							data-name="banner-desktop-video" class="fill-image">
							<source src="<?php echo $video_desktop_url; ?>" type="<?php echo $video_desktop_mime; ?>">
							Your browser does not support the video tag.
						</video>
						<video
							poster="<?php echo ( isset( $poster['url'] ) ? $poster['url'] : '' ); ?>"
							preload="none" muted loop playsinline
							data-name="banner-mobile-video" class="fill-image">
							<source src="<?php echo $video_mobile_url; ?>" type="<?php echo $video_mobile_mime; ?>">
							Your browser does not support the video tag.
						</video>
					<?php elseif ( get_field( 'twk_settings_video_type' ) === 'url' ) : ?>
						<?php if ( strpos( $video_url, '.m3u8' ) !== false ) : ?>
							<!-- Adaptive video -->
							<video
								data-name="adaptive-video"
								data-source="<?php echo $video_url; ?>"
								<?php echo( isset( $poster['url'] ) ? 'poster="' . $poster['url'] . '"' : '' ); ?>
								autoplay muted loop playsinline
								class="fill-image">
							</video>
						<?php else : ?>
							<!-- HTML Video (file or mp4 link) -->
							<video
								<?php echo( isset( $poster['url'] ) ? 'poster="' . $poster['url'] . '"' : '' ); ?>
								autoplay muted loop playsinline
								class="fill-image"
								>
								<source src="<?php echo $video_url; ?>" type="<?php echo $video_mime; ?>">
								Your browser does not support HTML5 video.
							</video>
						<?php endif; ?>
					<?php endif; ?>

				<?php else : ?>
					<?php echo twk_output_acf_image_with_fallback( get_field( 'banner_image' ), 'full', 'fill-image', 'page', 'eager', true ); ?>
				<?php endif; ?>
			</div>
		</div>
	</div>
	<div class="container">
		<div class="grid grid-cols-12">
			<div class="col-span-8 -mt-50">
				<h1 class="text-140ncb text-blue mix-blend-difference">
					<?php if ( get_field( 'banner_alternative_title' ) ) : ?>
						<?php echo get_field( 'banner_alternative_title' ); ?>
					<?php else : ?>
						<?php the_title(); ?>
					<?php endif; ?>
				</h1>
			</div>
			<?php if ( get_field( 'twk_settings_banner_menu' ) === 'skip' ) : ?>
				<div data-hidden-on-load data-column-break="right" class="col-span-3 col-start-10 -mt-150 relative z-10">
					<div data-name="banner-skip-links" class="custom-gradient py-40 px-50">
						<h3 class="text-46nb text-white mb-20">Skip to:</h3>

						<nav><ul class="space-y-10"></ul></nav><!-- Content is JS regenerated -->
					</div>
				</div>
			<?php elseif ( get_field( 'twk_settings_banner_menu' ) === 'menu' ) : ?>
				<div data-hidden-on-load data-column-break="right" class="col-span-3 col-start-10 -mt-150 relative z-10">
					<div data-name="banner-menu-links" class="custom-gradient py-40 px-50">
						<h3 class="text-46nb text-white mb-20">In this section</h3>

						<?php
						$args = array(
							'post_type'      => 'page',
							'posts_per_page' => '-1',
							'post_parent'    => $post->ID,
							'order'          => 'ASC',
							'orderby'        => 'menu_order',
						);
						$query = new WP_Query( $args );
						
						if ( $query->have_posts() ) :
							?>
							<nav>
								<ul class="space-y-10">
									<?php
									while ( $query->have_posts() ) :
										$query->the_post();
										?>
										<li><a href="<?php echo get_the_permalink(); ?>" class="text-20ncb text-white hover:text-yellow transition-color duration-300"><?php echo get_the_title(); ?></a></li>
									<?php endwhile; ?>
								</ul>
							</nav>
							<?php wp_reset_postdata(); ?>
						<?php elseif ( $post->post_parent ) : ?>
							<?php
							$current_page = get_the_ID();
							$args = array(
								'post_type'      => 'page',
								'posts_per_page' => '-1',
								'post_parent'    => $post->post_parent,
								'order'          => 'ASC',
								'orderby'        => 'menu_order',
							);
							$query = new WP_Query( $args );
							
							if ( $query->have_posts() ) :
								?>
								<nav>
									<ul class="space-y-10">
										<?php
										while ( $query->have_posts() ) :
											$query->the_post();
											$is_current_page = get_the_ID() === $current_page;
											?>
											<li>
												<a href="<?php echo get_the_permalink(); ?>" class="text-20ncb text-white hover:text-yellow transition-color duration-300 <?php echo( $is_current_page ? 'text-yellow' : '' ); ?>">
													<?php echo get_the_title(); ?>
												</a>
											</li>
										<?php endwhile; ?>
									</ul>
								</nav>
								<?php wp_reset_postdata(); ?>
							<?php endif; ?>
						<?php endif; ?>

					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</section>
