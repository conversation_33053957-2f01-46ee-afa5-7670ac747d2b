import { Carousel } from "@fancyapps/ui/dist/carousel/";
import "@fancyapps/ui/dist/carousel/carousel.css";

import { Autoplay } from "@fancyapps/ui/dist/carousel/carousel.autoplay.js";
import "@fancyapps/ui/dist/carousel/carousel.autoplay.css";

function basicSlider(){
	const sliders = document.querySelectorAll('[data-name="basic-slider"]');

	if ( sliders.length === 0 ) return;
	
	sliders.forEach(slider => {
		const options = {
			autoplay: {
				autoStart: true,
				timeout: 5000,
				pauseOnHover: true,
				showProgress: false
			},
			infinite: false,
			navigation: true,
			pagination: false,
		}

		Carousel(slider, options, { Autoplay }).init();
	});
}

export { basicSlider };