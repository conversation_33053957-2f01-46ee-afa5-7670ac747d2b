{"name": "twk-boilerplate-template", "version": "1.0.0", "description": "Template of the files that are created with create-twk-boilerplate", "main": "script.js", "scripts": {"css:editor": "node cssEditorStyles.js", "build:dev": "webpack --mode development --watch", "build:dev-mode": "webpack --env dev --mode development --watch", "build:prod": "webpack --mode production", "deploy": "node deploy", "tailwind": "npx @tailwindcss/cli -i ./src/styles/screen.css -o ./assets/css/styles.css --watch --content './**/*.{php,js}'", "tailwind:dev-mode": "concurrently \"npx @tailwindcss/cli -i ./src/styles/screen--dev.css -o ./assets/css/styles--dev.css --watch --content './**/*.{php,js}'\"", "tailwind:all": "concurrently \"npm run css:editor\" \"npx @tailwindcss/cli -i ./src/styles/screen.css -o ./assets/css/styles.css --minify --content './**/*.{php,js}'\" \"npx @tailwindcss/cli -i ./src/styles/admin-styles.css -o ./assets/css/admin-styles.css --minify --content './**/*.{php,js}'\" \"npx @tailwindcss/cli -i ./src/styles/editor-styles--bundle.css -o ./assets/css/editor-styles.css --minify --content './**/*.{php,js}'\"", "dev": "npm-run-all --parallel deploy build:dev tailwind", "prod": "npm-run-all --parallel deploy build:prod tailwind-all", "watch": "npm-run-all --parallel build:dev tailwind", "watch:dev-mode": "npm-run-all --parallel build:dev-mode tailwind:dev-mode"}, "author": "<EMAIL>", "license": "MIT", "devDependencies": {"@babel/core": "^7.13.10", "@babel/preset-env": "^7.13.10", "@tailwindcss/typography": "^0.5.18", "babel-loader": "^8.2.2", "basic-ftp": "^4.6.3", "concurrently": "^8.0.1", "node-notifier": "^8.0.2", "node-watch": "^0.6.4", "npm-run-all": "^4.1.5", "ssh2-sftp-client": "^7.2.1", "style-loader": "^4.0.0", "tailwindcss": "^4.0.0", "tailwindcss-debug-screens": "^3.0.1", "webpack": "^5.67.0", "webpack-build-notifier": "^2.3.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@fancyapps/ui": "^6.0.32", "css-loader": "^7.1.2", "gsap": "^3.13.0", "hls.js": "^1.5.17", "lenis": "^1.3.11", "set-scrollbar-width": "^1.0.5"}, "browserslist": ["last 5 versions"], "babel": {"presets": [["@babel/preset-env", {"modules": false}]]}}