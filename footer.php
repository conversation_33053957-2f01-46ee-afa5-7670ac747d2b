<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content-wrap div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package twkmedia
 */

?>

</div>
<!-- END DIV MAIN CONTENT -->

<footer data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" id="site-footer" class="bg-black py-30 text-white">
	<div class="container mb-30">
		<div class="grid grid-cols-12">
			<div class="col-span-4">
				<!-- Contact -->
				<?php if ( get_field( 'footer_contact_title', 'option' ) && ( get_field( 'footer_contact_address', 'option' ) || get_field( 'footer_contact_phone_number', 'option' ) || get_field( 'footer_contact_email', 'option' )  ) ) : ?>
					<h2><?php echo get_field( 'footer_contact_title', 'option' ); ?></h2>
				<?php endif; ?>

				<?php
				if ( $address = get_field( 'footer_contact_address', 'option' ) ) :
					$maps_url = 'https://www.google.com/maps/search/' . urlencode( $address );
					?>
					<address class="not-italic">
						<a href="<?php echo esc_url( $maps_url ); ?>" target="_blank" rel="noopener noreferrer">
							<?php echo $address; ?>
						</a>
					</address>
				<?php endif; ?>

				<?php if ( get_field( 'footer_contact_phone_number', 'option' ) ) : ?>
					<p>
						<a href="tel:<?php echo get_field( 'footer_contact_phone_number', 'option' ); ?>" rel="noopener noreferrer">
							<?php echo get_field( 'footer_contact_phone_number', 'option' ); ?>
						</a>
					</p>
				<?php endif; ?>

				<?php if ( get_field( 'footer_contact_email', 'option' ) ) : ?>
					<p>
						<a href="mailto:<?php echo get_field( 'footer_contact_email', 'option' ); ?>" rel="noopener noreferrer">
							<?php echo get_field( 'footer_contact_email', 'option' ); ?>
						</a>
					</p>
				<?php endif; ?>
			</div>
			<div class="col-span-4">
				<!-- Quick links -->
				<?php if ( have_rows( 'footer_quick_links', 'option' ) ) : ?>
					<nav aria-label="Quick links">
						<?php if ( get_field( 'footer_quick_links_title', 'option' ) ) : ?>
							<h2><?php echo get_field( 'footer_quick_links_title', 'option' ); ?></h2>
						<?php endif; ?>

						<ul>
							<?php
							while ( have_rows( 'footer_quick_links', 'option' ) ) :
								the_row();
								?>
								<li>
									<?php
									if ( get_sub_field( 'link', 'option' ) ) :
										echo twk_compose_acf_link( get_sub_field( 'link', 'option' ) );
									endif;
									?>
								</li>
								<?php
							endwhile;
							?>
						</ul>
					</nav>				
				<?php endif; ?>
			</div>
			<div class="col-span-4 children:justify-end">
				<!-- Social media -->
				<?php require locate_template( 'templates/parts/social-media-links.php' ); ?>
			</div>
		</div>
	</div>

	<div class="copyright">
		<div class="container">
			<div class="grid grid-cols-12 gap-15">
				<div class="col-span-8">
					<div class="flex items-center gap-15">
						© <?php echo get_bloginfo( 'name' ); ?> <?php echo date( 'Y' ); ?>

						<?php
						if ( have_rows( 'legal_menu', 'option' ) ) :
							while ( have_rows( 'legal_menu', 'option' ) ) :
								the_row();
								if ( get_sub_field( 'link', 'option' ) ) :
									echo twk_compose_acf_link( get_sub_field( 'link', 'option' ), 'mx-2' );
								endif;
							endwhile;
						endif;
						?>
					</div>
				</div>
				<div class="col-span-4">
					<div class="text-right text-white">
						<p class="inline mb-0"><a href="https://www.thewebkitchen.co.uk/" target="_blank" rel="external" aria-label="Go to The Web Kitchen website" class="text-inherit">Web design</a></p>
						<p class="inline mb-0">by</p>
						<p class="inline mb-0"><a href="https://www.thewebkitchen.co.uk/" target="_blank" rel="external" aria-label="Go to The Web Kitchen website" class="text-inherit">TWK</a></p>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>

</div>
<!-- JS ARE LOADED HERE WITH FUNCTION.PHP -->
<?php wp_footer(); ?>


</body>

</html>
