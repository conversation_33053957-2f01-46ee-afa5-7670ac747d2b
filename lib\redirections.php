<?php
/**
 * Redirections
 *
 * @package twkmedia
 */

/**
 * Gets the page id from the parameters on the URL.
 *
 * @return integer The page id if any. null if not found.
 */
function twk_get_page_id_from_url() {
	if ( isset( $_SERVER['QUERY_STRING'] ) ) {
		$visited_url = wp_unslash( $_SERVER['QUERY_STRING'] );
	} else {
		$visited_url = null;
	}

	$page_id     = null;
	$page        = null;
	$query_array = array();
	parse_str( $visited_url, $query_array );

	if ( array_key_exists( 'page_id', $query_array ) ) {
		$page_id = $query_array['page_id'];
	} elseif ( array_key_exists( 'p', $query_array ) ){
		$page_id = $query_array['p'];
	}

	if ( $page_id ) {
		$page = get_post( $page_id ); // to check if the page exists.
	}

	if ( $page ) {
		return $page_id;
	} else {
		return null;
	}
}

/**
 * Check before the 404 page.
 * Go to the login form if the link is for a draft page or private page.
 *
 * @return void
 */
function twk_before_404() {
	if ( ! is_404() ) {
		return;
	}

	// Try to get page ID from URL parameters.
	$page_id = twk_get_page_id_from_url();

	// If no page_id found, try to get the page from the request URI.
	if ( ! $page_id ) {
		$clean_uri = esc_url_raw( wp_unslash( $_SERVER['REQUEST_URI'] ) );
		$page      = get_post( url_to_postid( $clean_uri ) );

		if ( $page ) {
			$page_id = $page->ID;
		}
	}

	if ( $page_id ) {
		$post_status = get_post_status( $page_id );

		// Only redirect if the user is not logged in and the page exists.
		if ( ! is_user_logged_in() && $post_status ) {
			$redirect_needed = false;

			// Check for draft status.
			if ( in_array( $post_status, array( 'draft', 'pending', 'future' ) ) ) {
				$redirect_needed = true;
			}

			// Check for private status and user permissions.
			if ( $post_status === 'private' ) {
				$post = get_post( $page_id );
				$can_read = false;

				// Check default WordPress capabilities
				if ( current_user_can( 'read_private_posts' ) || current_user_can( 'read_private_pages' ) ) {
					$can_read = true;
				}

				if ( ! $can_read ) {
					$redirect_needed = true;
				}
			}

			if ( $redirect_needed ) {
				$redirect_url = wp_login_url();
				$redirect_url = add_query_arg( 'redirect_to', urlencode( get_permalink( $page_id ) ), $redirect_url );
				wp_safe_redirect( $redirect_url );
				exit();
			}
		} else if ( is_user_logged_in() ){
			if ( current_user_can( 'edit_posts' ) || current_user_can( 'edit_pages' ) ) {
				$user_can_preview = current_user_can( 'edit_post', $page_id ) || current_user_can( 'edit_page', $page_id );
				if ( $user_can_preview ) {
					return;
				}
			}

			$redirect_url = get_permalink( $page_id );
			wp_safe_redirect( $redirect_url );
			exit();
		}
	}
}
add_action( 'template_redirect', 'twk_before_404' );

/**
 * After login checks if there is a page ID on the URL and redirects to it.
 *
 * @param string $url The redirect destination URL.
 * @param string $request The requested redirect destination URL passed as a parameter.
 * @param object $user WP_User object if login was successful, WP_Error object otherwise.
 * @return string
 */
function twk_after_login_redirect( $url, $request, $user ) {
	if ( ! $user || is_wp_error( $user ) ) {
		return $url;
	}

	// If there's a specific redirect_to parameter, use that.
	if ( ! empty( $request ) ) {
		$post_id = url_to_postid( $request );
		if ( $post_id ) {
			$post_status = get_post_status( $post_id );
			
			// Check user permissions for the specific post.
			if ( $post_status === 'private' && ( current_user_can( 'read_private_posts' ) || current_user_can( 'read_private_pages' ) ) ) {
				return $request;
			}
			
			if ( in_array( $post_status, array( 'draft', 'pending', 'future' ) ) && ( current_user_can( 'edit_posts' ) || current_user_can( 'edit_pages' ) ) ) {
				return $request;
			}
		}
	}

	// Fallback to the page_id parameter if no specific redirect.
	$page_id = twk_get_page_id_from_url();
	if ( $page_id && get_post( $page_id ) ) {
		return get_permalink( $page_id );
	}

	return $url;
}
add_filter( 'login_redirect', 'twk_after_login_redirect', 10, 3 );
