<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content-wrap">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package twkmedia
 */

?>
<!DOCTYPE html>
<html class="no-js" <?php language_attributes(); ?>>

<head>
	<?php if ( strpos( home_url(), 'twkmedia.com' ) == false ) : ?>
	<!-- Google Tag Manager -->
	<script>
	(function(w,d,s,l,i){
		w[l]=w[l]||[];
		w[l].push( {'gtm.start': new Date().getTime(),event:'gtm.js'});
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
		j.async=true;
		j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-XXXXXXX');
	</script>
	<!-- End Google Tag Manager -->
	<?php endif; ?>

	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<!-- Always force latest IE rendering engine (even in intranet) & Chrome Frame -->
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<?php if ( is_search() ) : ?>
	<meta name="robots" content="noindex, nofollow" />
	<?php endif; ?>

	<!-- Fonts -->
	<link rel="stylesheet" href="https://use.typekit.net/sfa6xtf.css">

	<!-- SETUP AND AUTHENTICATE WITH TWK CLIENTS USER http://google.com/webmasters -->
	<meta name="google-site-verification" content="">

	<!-- GENERATE FAVICON USING https://realfavicongenerator.net/ -->

	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="pingback" href="<?php bloginfo( 'pingback_url' ); ?>" />

	<?php wp_head(); ?>
</head>


<?php
if ( is_user_logged_in() && ( get_current_user_id() === 1 || get_current_user_id() === 2 ) ) {
	/* Grid Overlay */
	include locate_template( 'templates/parts/grid-overlay.php' );
}
if ( is_user_logged_in() ) {
	/* Dev mode switcher */
	include locate_template( 'lib/dev-mode.php' );
}
?>


<body <?php body_class( get_current_user_id() === 1 || get_current_user_id() === 2 ? 'debug-screens' : '' ); ?>>

	<?php if ( strpos( home_url(), 'twkmedia.com' ) == false ) : ?>
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXXX"
	height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
	<?php endif; ?>


	<?php
	/* TWK notifications system */
	require locate_template( 'templates/sections/notifications/notifications.php' );
	?>


	<?php
	// The blog index page needs to get the queried ID
	// and not the first post ID.
	global $post;
	$post = get_queried_object();

	edit_post_link( 'Edit page', '', '', '', 'fixed z-[90] top-0 left-0 bg-black text-white py-5 px-10' );
	?>

	<div data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" id="page-wrap">
		<a class="skip-link sr-only" href="#content-wrap"><?php esc_html_e( 'Skip to content' ); ?></a>

		<nav data-name="top-nav" class="absolute z-50 top-0 left-0 w-full py-32">
			<div class="container-fluid">
				<div class="grid grid-cols-12 gap-15">
					<div class="col-span-2">
						<a href="<?php echo home_url(); ?>">
							<div><img class="w-150 h-auto" src="<?php echo get_template_directory_uri(); ?>/assets/img/logos/logo.png" alt="Site logo" /></div>
						</a>
					</div>
					<div class="col-span-5 lg:col-span-9 text-right">
						<?php
						if ( has_nav_menu( 'main_menu' ) ) {
							wp_nav_menu(
								array(
									'theme_location' => 'main_menu',
									'menu_id'        => 'main-menu',
									'menu_class'     => 'menu flex gap-15 justify-end text-white',
									'walker'         => new Twk_Nav_Walker(),
								)
							);
						}
						?>
					</div>
				</div>
			</div>
		</nav>
		<div id="content-wrap">
