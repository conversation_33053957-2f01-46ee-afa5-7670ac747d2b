<?php
/**
 * Template part to display the block called "Accordion".
 *
 * @package twkmedia
 */

?>

<div data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="container">
	<?php if ( get_sub_field( 'accordion_title' ) ) : ?>
		<div class="flex flex-wrap justify-center">
			<div class="w-full md:w-10/12">
				<h2><?php echo get_sub_field( 'accordion_title' ); ?></h2>
			</div>
		</div>
	<?php endif; ?>
	<!-- --------------------------------
		ACCORDION
	-------------------------------- -->
	<?php if ( have_rows( 'accordion' ) ) : ?>
		<div class="flex flex-wrap justify-center">
			<div class="w-full md:w-10/12">
				<div class="py-10" aria-label="Accordions">
					<?php
					while ( have_rows( 'accordion' ) ) :
						the_row();
						?>

						<div class="group" data-target="accordion" data-state="closed">
							<div class="flex justify-between items-center cursor-pointer border-b-2 border-black" data-target="accordion-trigger">
								<h3 class="accordion__title title title--md m-0">
									<?php echo get_sub_field( 'title' ); ?>
								</h3>

								<div class="icon">
									<!-- ARROW -->
									<!-- <svg width="18px" height="10px" viewBox="0 0 18 10" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
										<g id="Home-v3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(-440.000000, -785.000000)">
											<g id="Page-1" transform="translate(440.000000, 785.000000)" fill="#13212e">
												<path d="M5.95082782,12.8924648 L13.6658265,5.39228986 C13.8252119,5.23681359 13.9163654,5.02277461 13.9163654,4.80303482 C13.9163654,4.58381329 13.8252119,4.36873779 13.6658265,4.21377979 L5.95082782,-3.28639512 C5.61606522,-3.61134051 5.07340864,-3.61134051 4.7391791,-3.28639512 C4.40441649,-2.96093148 4.40441649,-2.4333487 4.7391791,-2.10788505 L11.8475538,4.80303482 L4.7391791,11.7139547 C4.40441649,12.0394183 4.40441649,12.5670011 4.7391791,12.8924648 C5.07340864,13.2179284 5.61606522,13.2179284 5.95082782,12.8924648" id="Fill-1" transform="translate(9.202236, 4.803229) rotate(90.000000) translate(-9.202236, -4.803229) "></path>
											</g>
										</g>
									</svg> -->

									<!-- PLUS -->
									<svg class='w-40 h-40' viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path class="plus-horizontal" d="M32.6066 22H11.3934" stroke="#000000" stroke-width="2" />
										<path class="plus-vertical" d="M22 11.3934V32.6066" stroke="#000000" stroke-width="2" />
									</svg>
								</div>
							</div>

							<div class="prose h-0 overflow-hidden" aria-hidden="true" data-target="accordion-content">
								<div class="p-5"><?php echo get_sub_field( 'content' ); ?></div>
							</div>
						</div>

					<?php endwhile; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>
	<!-- END ACCORDION -->
</div>
