export default function(){
	const tabsContainer = document.querySelectorAll('[data-name="tabs-container"]');

	if ( tabsContainer.length === 0 ) return;

	tabsContainer.forEach(container => {
		const tabList   = container.querySelector('[role="tablist"]');
		const tabs      = Array.from(container.querySelectorAll('[role="tab"]'));
		const tabPanels = Array.from(container.querySelectorAll('[role="tabpanel"]'));

		let focusedTab = 0;

		tabList.addEventListener("keydown", handleTabListKeydown);

		for (const tab of tabs) {
			tab.addEventListener("click", handleTabClick);
		}

		function updateTabFocus() {
			for (const tab of tabs) {
				tab.setAttribute("tabindex", -1);
			}
			tabs[focusedTab].setAttribute("tabindex", 0);
			tabs[focusedTab].focus();
		}

		function handleTabListKeydown(event) {
			const key = event.key;

			if (key === "ArrowRight" || key === "ArrowLeft") {
				if (key === "ArrowRight") {
					focusedTab++;
					if (focusedTab >= tabs.length) {
						focusedTab = 0;
					}
				} else if (key === "ArrowLeft") {
					focusedTab--;
					if (focusedTab < 0) {
						focusedTab = tabs.length - 1;
					}
				}
				updateTabFocus();
			}
		}

		function handleTabClick(event) {
			const target = event.target;
			const selectedTabIndex = tabs.indexOf(target);

			for (const tab of tabs) {
				tab.setAttribute("aria-selected", false);
			}
			target.setAttribute("aria-selected", true);

			for (const panel of tabPanels) {
				panel.setAttribute("hidden", true);
			}
			container
				.querySelector(`#${target.getAttribute("aria-controls")}`)
				.removeAttribute("hidden");

			focusedTab = selectedTabIndex;
			updateTabFocus();
		}
	});

	// If the URL has a param tab, open the tab with the data-name with that param.
	// If the block is after the fold, we scroll to it.
	const urlParams = new URLSearchParams(window.location.search);
	const tabParam = urlParams.get('tab');
	
	if ( tabParam ) {
		const tab = document.querySelector(`[data-name="${tabParam}"]`);

		if ( tab ) {
			setTimeout(() => {
				const block = tab.closest('.block');
				if ( block ) {
					const headerOffset = 50;
					const elementPosition = block.getBoundingClientRect().top;
					const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
	
					window.scrollTo({
						top: offsetPosition,
						behavior: 'smooth'
					});
				}
			}, 100);

			tab.click();
		}
		
	}
	
}