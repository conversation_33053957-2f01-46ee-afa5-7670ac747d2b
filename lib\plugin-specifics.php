<?php
/**
 * Plugin specifics
 *
 * @package twkmedia
 */

/*
	==================================================================
		GRAVITY FORMS
	==================================================================
*/
/**
 * Permissions/capabilities for Gravity Forms
 *
 * @return void
 */
function twk_add_gf_cap() {
	$editor = get_role( 'editor' );
	$editor->add_cap( 'gravityforms_edit_forms' );
	$editor->add_cap( 'gravityforms_delete_forms' );
	$editor->add_cap( 'gravityforms_create_form' );
	$editor->add_cap( 'gravityforms_view_entries' );
	$editor->add_cap( 'gravityforms_edit_entries' );
	$editor->add_cap( 'gravityforms_delete_entries' );
	$editor->add_cap( 'gravityforms_view_settings' );
	$editor->add_cap( 'gravityforms_edit_settings' );
	$editor->add_cap( 'gravityforms_export_entries' );
	$editor->add_cap( 'gravityforms_view_entry_notes' );
	$editor->add_cap( 'gravityforms_edit_entry_notes' );
}
add_action( 'admin_init', 'twk_add_gf_cap' );


/**
 * Disable form IP storage on all forms.
 */
add_filter( 'gform_ip_address', '__return_empty_string' );



/*
	==================================================================
		THE EVENTS CALENDAR
	==================================================================
*/
/* Increases the number of exported events by default on subscriptions */
if ( class_exists( 'Tribe__Events__Main' ) ) {
	add_filter( 'tribe_ical_feed_posts_per_page', function() { return 300; } );
}


/*
	==================================================================
		RELEVANSSI
	==================================================================
*/
/**
 * Relevanssi add content to custom excerpts.
 *
 * @param [type] $content Content.
 * @param [type] $post Post.
 * @param [type] $query Query.
 * @return $content
 */
function twk_custom_fields_to_excerpts( $content, $post, $query ) {

	$custom_fields            = get_post_custom_keys( $post->ID );
	$remove_underscore_fields = true;

	if ( is_array( $custom_fields ) ) {
		$custom_fields = array_unique( $custom_fields ); // no reason to index duplicates.

		foreach ( $custom_fields as $field ) {

			if ( $remove_underscore_fields ) {
				if ( '_' === substr( $field, 0, 1 ) ) {
					continue;
				}
			}

			// Remove unwanted fields from the excerpt (fields created only for layout or admin purpose)
			if ( str_starts_with( $field, 'twk_settings_' ) || str_starts_with( $field, 'twk_hidden_' ) ) {
				continue;
			}

			$values = get_post_meta( $post->ID, $field, false );

			if ( '' === $values ) {
				continue;
			}
			foreach ( $values as $value ) {
				if ( ! is_array( $value ) ) {
					$content .= ' ' . $value;
				}
			}
		}
	}

	return $content;
}
add_filter( 'relevanssi_excerpt_content', 'twk_custom_fields_to_excerpts', 10, 3 );




/*
	==================================================================
		ADVANCED CUSTOM FIELDS (ACF)
	==================================================================
*/

/**
 * Add new location for ACF json data.
 *
 * @param [type] $path Path.
 * @return $path
 */
function twk_acf_json_save_point( $path ) {
	/* if ( isset( $_POST ) && isset( $_POST['post_title'] ) ) {
		preg_match_all("/\[([^\]]*)\]/", $_POST['post_title'], $matches);
		$block_category   = $matches[1][0];
		$component_folder = sanitize_title( str_replace( '[' . $block_category . '] - ', '', $_POST['post_title'] ) );

		if ( $block_category === 'Component' ){
			$path = get_stylesheet_directory() . '/blocks/' . $component_folder;
		} elseif ( $block_category === 'Block'  ) {
			$path = get_stylesheet_directory() . '/blocks/blocks/' . $component_folder;
		}
	} else {
		$path = get_stylesheet_directory() . '/blocks/acf-json';
	}

	if (strtoupper(substr(PHP_OS, 0, 3)) == 'WIN') {
        $path = str_replace('/', '\\', $path);
    }

	return $path; */

	$path = get_stylesheet_directory() . '/lib/acf-json';

	return $path;
}
add_filter( 'acf/settings/save_json', 'twk_acf_json_save_point', 20 );

/**
 * Undocumented function
 *
 * @param [type] $paths Paths.
 * @return $paths
 */
function twk_acf_json_load_point( $paths ) {
	// remove original path (optional).
	unset( $paths[0] );

	// append path.
	$paths[] = get_stylesheet_directory() . '/lib/acf-json';

	// return.
	return $paths;
}
add_filter( 'acf/settings/load_json', 'twk_acf_json_load_point' );




/**
 * Set default flexible content strips. Starter template.
 *
 * @param [type] $value
 * @param [type] $post_id
 * @param [type] $field
 * @return void
 */
function twk_default_blocks( $value, $post_id, $field ) {

	// Only add default content for new posts.
	if ( $value !== null ) {
		return $value;
	}

	// Only add default content for certain post types.
	if ( ! in_array( get_post_type( $post_id ), array( 'page' ), true ) ) {
		return $value;
	}

	$value = array(
		array(
			'acf_fc_layout' => 'text',
		),
	);

	return $value;
}
add_filter( 'acf/load_value/name=blocks', 'twk_default_blocks', 10, 3 );





/**
 * Function that runs after a post is saved or udpated.
 *
 * @return void
 */
function twk_acf_save_post() {

	$screen = get_current_screen(); // We need to know what screen on the backend was saved.

	/**
	 * Popup.
	 */
	if (
		strpos( $screen->id, 'notifications' ) == true && get_field( 'popup_reset', 'option' ) ||
		get_field( 'popup_cookies_name', 'option' ) === ''
		) {
		// 'global-settings_page_notifications' is the slug for the "option page" of ACF where we manage the notifications fields
		// 'cookies_bar_reset' is a true/false field that we set to true when we want to reset the cookies
		$cookie_name = 'twk_cookies_popup_' . wp_rand( 1, 99999999999999999 ); // generates a new cookie number.

		update_field( 'popup_cookies_name', $cookie_name, 'option' );
		// 'popup_cookies_name' is a hidden field that controls the name of the cookie (you can add a class of hidden to the field and this won't show up on the backend)
	}
}
add_action( 'acf/save_post', 'twk_acf_save_post', 20 );




/**
 * Slugifies the custom ID if one provided.
 *
 * @param integer $post_id The Post ID.
 * @return void
 */
function twk_clean_custom_block_id( $post_id ) {

	$strips    = 'blocks';
	$custom_id = 'twk_settings_block_custom_id';

	if ( get_post_meta( $post_id, $strips, true ) ) {
		$count = count( get_post_meta( $post_id, $strips, true ) );

		for ( $i = 0; $i <= $count; $i++ ) {
			$custom_id_acf_name = $strips . '_' . $i . '_' . $custom_id;
			$value_custom_id    = get_post_meta( $post_id, $custom_id_acf_name, true );

			if ( $value_custom_id !== '' ) {
				$new_value_custom_id = sanitize_title( $value_custom_id );

				update_post_meta( $post_id, $custom_id_acf_name, $new_value_custom_id );
			} else {
				$new_value_custom_id = 'block-' . wp_generate_uuid4();

				update_post_meta( $post_id, $custom_id_acf_name, $new_value_custom_id );
			}
		}
	}

}
add_action( 'acf/save_post', 'twk_clean_custom_block_id' );



/**
 * Add a reference title on the header for Flexible content.
 *
 * @param [type] $title
 * @param [type] $field
 * @param [type] $layout
 * @param [type] $i
 * @return void
 */
function twk_acf_fields_flexible_content_layout_title( $title, $field, $layout, $i ) {
	// load text sub field
	if ( get_sub_field('twk_settings_easy_identifier') ) {
		$title .= ' - <b>' . strip_tags( get_sub_field('twk_settings_easy_identifier') ) . '</b>';
	} elseif ( get_sub_field('title') && gettype( get_sub_field('title') ) === 'string' ) {
		$title .= ' - <b>' . strip_tags( get_sub_field('title') ) . '</b>';
	}
	return $title;
}
add_filter( 'acf/fields/flexible_content/layout_title', 'twk_acf_fields_flexible_content_layout_title', 10, 4 );



/**
 * It sets the featured image from a ACF field if it's not set.
 *
 * @param integer $post_id
 * @return void
 */
function twk_set_featured_image_from_acf( $post_id ) {
    if ( get_field( 'banner_image', $post_id ) && isset( get_field( 'banner_image', $post_id )['ID'] ) ) {
        $banner_image = get_field( 'banner_image', $post_id )['ID'];

        if ( ! has_post_thumbnail( $post_id ) ) {
            set_post_thumbnail( $post_id, $banner_image );
        }
    }
}
add_action( 'acf/save_post', 'twk_set_featured_image_from_acf');



function twk_acf_init_google_maps_backend() {
	global $GOOGLE_MAPS_API_KEY;
	
	if ( $GOOGLE_MAPS_API_KEY ) {
		acf_update_setting('google_api_key', $GOOGLE_MAPS_API_KEY);
	}
}

add_action('acf/init', 'twk_acf_init_google_maps_backend');





/*
	==================================================================
		ACF Extended
	==================================================================
*/

// Disable ACF Extended Options Pages Module
function twk_disable_acfextended_modules(){
    acf_update_setting('acfe/modules/options_pages', false);
    acf_update_setting('acfe/modules/block_types', false);
    acf_update_setting('acfe/modules/forms', false);
}
add_action('acf/init', 'twk_disable_acfextended_modules');



/**
 * Add a thumbnail to the ACF Flexible Content
 *
 * @param [type] $thumbnail
 * @param [type] $field
 * @param [type] $layout
 * @return void
 */
function twk_acfe_layout_thumbnail($thumbnail, $field, $layout){
	$layout = str_replace( '_', '-', $layout['name'] );

	// Must return an URL or Attachment ID
    return get_template_directory_uri().'/assets/img/blocks-thumbnails/' . $layout . '.png';  
}
add_filter('acfe/flexible/thumbnail/name=blocks', 'twk_acfe_layout_thumbnail', 10, 3);



/*
	==================================================================
		Yoast SEO
	==================================================================
*/
/**
 * Move Yoast to bottom
 *
 * @return String low priority
 */
function twk_yoast_to_bottom() {
	return 'low';
}
add_filter( 'wpseo_metabox_prio', 'twk_yoast_to_bottom' );





/*
	==================================================================
		WP Rocket
	==================================================================
*/

/**
 * Allow editors to clear WP Rocket cache.
 *
 * @return void
 */
function wp_rocket_add_purge_posts_to_author() {
	// gets the author role object.
	$role = get_role( 'editor' );

	// add a new capability.
	$role->add_cap( 'rocket_purge_cache', true );
	//$role->add_cap( 'rocket_purge_cloudflare_cache', true );
}
add_action( 'init', 'wp_rocket_add_purge_posts_to_author', 12 );




/*
	==================================================================
		Tablepress
	==================================================================
*/
// Remove tablepress styles
add_filter( 'tablepress_use_default_css', '__return_false' );


/**
 * Turn off the usage of DataTables by default in the page template for new tables
 *
 * @since 1.0
 *
 * @param array $table Table template.
 * @return array Modified table template.
 */
function tablepress_turn_off_datatables_new_tables( $table ) {
  $table['options']['use_datatables'] = false;
	return $table;
}
add_filter( 'tablepress_table_template', 'tablepress_turn_off_datatables_new_tables' );