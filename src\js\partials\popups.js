import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";

function regularPopup() {	
	Fancybox.bind('[data-name="popup"]', {
		// Main class to style the popup
		mainClass: 'fancybox-regular-popup',

		// Carousel plugin options.
		Carousel: {
			friction: 0.8,
			preload: 2,
			infinite: true,
			Toolbar: false, // https://fancyapps.com/fancybox/guides/toolbar/
			Thumbs: false,  // https://fancyapps.com/fancybox/guides/thumbnails/
		},
	});
}

function popups(){
	regularPopup()
}

export default popups;
