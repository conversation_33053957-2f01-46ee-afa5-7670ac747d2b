<?php
/**
 * The template for displaying search results pages.
 *
 * @package twkmedia
 */

get_header();
?>

<!-- BANNER -->
<section data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="relative text-center mb-60 pb-70 pt-120 lg:pt-120 lg:pb-50">
	<div class="container">
		<div class="xl:w-8/12 mx-auto">
			<h1 class="mb-20">Search results</h1>
			<div class="mx-auto [&_form]:relative lg:w-[670px] [&_.screen-reader-text]:hidden [&_.search-field]:bg-[transparent] [&_.search-field]:w-full [&_.search-field]:outline-none [&_.search-field]:py-20 [&_.search-field]:pl-25 [&_.search-field]:pr-100
				[&_.search-submit]:absolute [&_.search-submit]:top-0 [&_.search-submit]:right-0 [&_.search-submit]:w-100 [&_.search-submit]:h-full [&_.search-submit]:indent-[9999px] [&_.search-submit]:block [&_.search-submit]:bg-search-dark 
				[&_.search-submit]:bg-no-repeat [&_.search-submit]:bg-center [&_.search-submit]:cursor-pointer [&_.search-field]:placeholder-black
				">
					<?php get_search_form(); ?>
			</div>
		</div>
	</div>
</section>

<div data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="container">
	<div class="xl:w-10/12 mx-auto">
		<?php if ( get_query_var('s') ) : ?>
			<h2 class="mt-15">
				<?php echo 'Your search results for "' . esc_attr( sanitize_text_field( get_query_var('s') ) ) . '"'; ?>
			</h2>
		<?php endif; ?>

		<?php
		if ( have_posts() ) :
			while ( have_posts() ) :
				the_post();
				?>

				<article <?php post_class( 'search-result space-y-30' ); ?> id="post-<?php get_the_ID(); ?>">
					<div class="grid grid-cols-12 gap-x-30 gap-y-30 border-b border-black/20 mb-50 pb-50">
						<div class="col-span-9 flex flex-col justify-center">
							<a href="<?php echo get_permalink(); ?>">
								<?php $current_url = str_replace( home_url(), '', get_permalink() ); ?>
								<span class=""><?php echo $current_url; ?></span>

								<h3 class="mt-10 mb-10"><?php the_title(); ?></h3>
							</a>
							<div class="text-[#80878A]"><?php echo twk_excerpt(16); ?></div>
						</div>
						<div class="col-span-3">
							<div class="relative aspect-[400/280]">
								<?php echo twk_output_featured_image_with_fallback( get_the_ID(), 'medium', 'fill-image', 'lazy', get_post_type() ) ?>
							</div>
						</div>
					</div>
				</article>

				<?php
				wp_reset_postdata();
			endwhile;
			?>
			<div class="mx-auto mt-100">
				<?php require locate_template( 'templates/parts/_pagination.php' ); ?>
			</div>

		<?php else : ?>
			<div class="mt-30 mb-80">
				<p>No results found.</p>
				<a href="/" class="button mt-20">Back to homepage</a>
			</div>
		<?php endif; ?>
	</div>
</div>


<?php
get_footer();
