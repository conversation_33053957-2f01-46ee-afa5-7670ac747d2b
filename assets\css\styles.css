/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: "nitti-grotesk", sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-black: #072252;
    --color-white: #fff;
    --spacing: 1px;
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 700;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --font-nitti-grotesk: "nitti-grotesk", sans-serif;
    --font-nitti-grotesk-condensed: "nitti-grotesk-condensed", sans-serif;
    --color-blue: #0F4DBC;
    --color-yellow: #EBB700;
    --color-gradient-start: #B71234;
    --color-gradient-end: #5A3188;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .stretched-link {
    &::after {
      content: var(--tw-content);
      pointer-events: auto;
    }
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
    &::after {
      content: var(--tw-content);
      inset: calc(var(--spacing) * 0);
    }
    &::after {
      content: var(--tw-content);
      z-index: 1;
    }
    &::after {
      content: var(--tw-content);
      background-color: transparent;
    }
  }
  .overlay {
    pointer-events: none;
    position: absolute;
    z-index: 1;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .fill-image {
    position: absolute;
    inset: calc(var(--spacing) * 0);
    height: 100%;
    width: 100%;
    object-fit: cover;
    &>* {
      position: absolute;
    }
    &>* {
      inset: calc(var(--spacing) * 0);
    }
    &>* {
      height: 100%;
    }
    &>* {
      width: 100%;
    }
    &>* {
      object-fit: cover;
    }
    &:is(*) {
      position: absolute;
    }
    &:is(*) {
      inset: calc(var(--spacing) * 0);
    }
    &:is(*) {
      height: 100%;
    }
    &:is(*) {
      width: 100%;
    }
    &:is(*) {
      object-fit: cover;
    }
  }
  .debug-screens {
    &::before {
      position: fixed;
      z-index: 2147483647;
      top: 0;
      right: 0;
      padding: .3333333em .5em;
      font-size: 12px;
      line-height: 1;
      font-family: sans-serif;
      background-color: #000;
      color: #fff;
      box-shadow: 0 0 0 1px #fff;
      content: 'screen: _';
      @media (min-width: 340px) {
        content: 'screen: xs';
      }
      @media (min-width: 576px) {
        content: 'screen: sm';
      }
      @media (min-width: 768px) {
        content: 'screen: md';
      }
      @media (min-width: 992px) {
        content: 'screen: lg';
      }
      @media (min-width: 1280px) {
        content: 'screen: xl';
      }
      @media (min-width: 1536px) {
        content: 'screen: 2xl';
      }
      @media (min-width: 1920px) {
        content: 'screen: 3xl';
      }
    }
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip-path: inset(50%);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .overlay--full {
    inset: calc(var(--spacing) * 0);
    background-color: color-mix(in srgb, #072252 25%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 25%, transparent);
    }
  }
  .overlay--bottom {
    inset: calc(var(--spacing) * 0);
    background-image: linear-gradient(180deg,rgba(0,0,0,0) 66.45%,rgba(0,0,0,0.4) 99.85%);
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .overlay--top {
    top: calc(var(--spacing) * 0);
    right: calc(var(--spacing) * 0);
    left: calc(var(--spacing) * 0);
    height: 100%;
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
    --tw-gradient-from: color-mix(in srgb, #072252 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-via: color-mix(in srgb, #072252 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
    --tw-gradient-via-position: 25%;
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-10 {
    top: calc(var(--spacing) * 10);
  }
  .top-\[10dvh\] {
    top: 10dvh;
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .right-30 {
    right: calc(var(--spacing) * 30);
  }
  .right-65 {
    right: calc(var(--spacing) * 65);
  }
  .right-105 {
    right: calc(var(--spacing) * 105);
  }
  .-bottom-60 {
    bottom: calc(var(--spacing) * -60);
  }
  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }
  .bottom-30 {
    bottom: calc(var(--spacing) * 30);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-30 {
    left: calc(var(--spacing) * 30);
  }
  .isolate {
    isolation: isolate;
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[90\] {
    z-index: 90;
  }
  .z-\[999\] {
    z-index: 999;
  }
  .z-\[9999\] {
    z-index: 9999;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-span-3 {
    grid-column: span 3 / span 3;
  }
  .col-span-4 {
    grid-column: span 4 / span 4;
  }
  .col-span-5 {
    grid-column: span 5 / span 5;
  }
  .col-span-8 {
    grid-column: span 8 / span 8;
  }
  .col-span-9 {
    grid-column: span 9 / span 9;
  }
  .col-span-10 {
    grid-column: span 10 / span 10;
  }
  .col-span-12 {
    grid-column: span 12 / span 12;
  }
  .col-start-2 {
    grid-column-start: 2;
  }
  .col-start-3 {
    grid-column-start: 3;
  }
  .col-start-10 {
    grid-column-start: 10;
  }
  .float-right {
    float: right;
  }
  .container {
    width: 100%;
    @media (width >= 340px) {
      max-width: 340px;
    }
    @media (width >= 576px) {
      max-width: 576px;
    }
    @media (width >= 768px) {
      max-width: 768px;
    }
    @media (width >= 992px) {
      max-width: 992px;
    }
    @media (width >= 1280px) {
      max-width: 1280px;
    }
    @media (width >= 1536px) {
      max-width: 1536px;
    }
    @media (width >= 1920px) {
      max-width: 1920px;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-20 {
    margin-block: calc(var(--spacing) * 20);
  }
  .my-40 {
    margin-block: calc(var(--spacing) * 40);
  }
  .my-50 {
    margin-block: calc(var(--spacing) * 50);
  }
  .my-80 {
    margin-block: calc(var(--spacing) * 80);
  }
  .my-\[10px\] {
    margin-block: 10px;
  }
  .prose {
    color: var(--tw-prose-body);
    max-width: 65ch;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-lead);
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-links);
      text-decoration: underline;
      font-weight: 500;
    }
    :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-bold);
      font-weight: 600;
    }
    :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      font-weight: 400;
      color: var(--tw-prose-counters);
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      color: var(--tw-prose-bullets);
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.25em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-color: var(--tw-prose-hr);
      border-top-width: 1px;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-style: italic;
      color: var(--tw-prose-quotes);
      border-inline-start-width: 0.25rem;
      border-inline-start-color: var(--tw-prose-quote-borders);
      quotes: "\201C""\201D""\2018""\2019";
      margin-top: 1.6em;
      margin-bottom: 1.6em;
      padding-inline-start: 1em;
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 800;
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 700;
      font-size: 1.5em;
      margin-top: 2em;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      font-size: 1.25em;
      margin-top: 1.6em;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      display: block;
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-family: inherit;
      color: var(--tw-prose-kbd);
      box-shadow: 0 0 0 1px var(--tw-prose-kbd-shadows), 0 3px 0 var(--tw-prose-kbd-shadows);
      font-size: 0.875em;
      border-radius: 0.3125rem;
      padding-top: 0.1875em;
      padding-inline-end: 0.375em;
      padding-bottom: 0.1875em;
      padding-inline-start: 0.375em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-code);
      font-weight: 600;
      font-size: 0.875em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: "`";
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: "`";
    }
    :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-pre-code);
      background-color: var(--tw-prose-pre-bg);
      overflow-x: auto;
      font-weight: 400;
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-inline-end: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-inline-start: 1.1428571em;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      background-color: transparent;
      border-width: 0;
      border-radius: 0;
      padding: 0;
      font-weight: inherit;
      color: inherit;
      font-size: inherit;
      font-family: inherit;
      line-height: inherit;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: none;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: none;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      width: 100%;
      table-layout: auto;
      margin-top: 2em;
      margin-bottom: 2em;
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-th-borders);
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-td-borders);
    }
    :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 0;
    }
    :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: baseline;
    }
    :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-prose-th-borders);
    }
    :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: top;
    }
    :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    --tw-prose-body: oklch(37.3% 0.034 259.733);
    --tw-prose-headings: oklch(21% 0.034 264.665);
    --tw-prose-lead: oklch(44.6% 0.03 256.802);
    --tw-prose-links: oklch(21% 0.034 264.665);
    --tw-prose-bold: oklch(21% 0.034 264.665);
    --tw-prose-counters: oklch(55.1% 0.027 264.364);
    --tw-prose-bullets: oklch(87.2% 0.01 258.338);
    --tw-prose-hr: oklch(92.8% 0.006 264.531);
    --tw-prose-quotes: oklch(21% 0.034 264.665);
    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-captions: oklch(55.1% 0.027 264.364);
    --tw-prose-kbd: oklch(21% 0.034 264.665);
    --tw-prose-kbd-shadows: color-mix(in oklab, oklch(21% 0.034 264.665) 10%, transparent);
    --tw-prose-code: oklch(21% 0.034 264.665);
    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);
    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);
    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);
    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: rgb(255 255 255 / 10%);
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      padding-inline-start: 1.625em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.5714286em;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .-mt-40 {
    margin-top: calc(var(--spacing) * -40);
  }
  .-mt-50 {
    margin-top: calc(var(--spacing) * -50);
  }
  .-mt-150 {
    margin-top: calc(var(--spacing) * -150);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-15 {
    margin-top: calc(var(--spacing) * 15);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-30 {
    margin-top: calc(var(--spacing) * 30);
  }
  .mt-70 {
    margin-top: calc(var(--spacing) * 70);
  }
  .mt-100 {
    margin-top: calc(var(--spacing) * 100);
  }
  .mt-345 {
    margin-top: calc(var(--spacing) * 345);
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .mb-25 {
    margin-bottom: calc(var(--spacing) * 25);
  }
  .mb-30 {
    margin-bottom: calc(var(--spacing) * 30);
  }
  .mb-40 {
    margin-bottom: calc(var(--spacing) * 40);
  }
  .mb-50 {
    margin-bottom: calc(var(--spacing) * 50);
  }
  .mb-60 {
    margin-bottom: calc(var(--spacing) * 60);
  }
  .mb-80 {
    margin-bottom: calc(var(--spacing) * 80);
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .table {
    display: table;
  }
  .aspect-\[1\/1\] {
    aspect-ratio: 1/1;
  }
  .aspect-\[10\/2\] {
    aspect-ratio: 10/2;
  }
  .aspect-\[10\/12\] {
    aspect-ratio: 10/12;
  }
  .aspect-\[14\/12\] {
    aspect-ratio: 14/12;
  }
  .aspect-\[400\/280\] {
    aspect-ratio: 400/280;
  }
  .aspect-\[1230\/600\] {
    aspect-ratio: 1230/600;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .scroll-lock {
    height: 100vh;
    overflow: hidden;
    width: calc((100% - var(--twcb-scrollbar-width)));
  }
  .size-80 {
    width: calc(var(--spacing) * 80);
    height: calc(var(--spacing) * 80);
  }
  .h-0 {
    height: calc(var(--spacing) * 0);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-\[70vh\] {
    max-height: 70vh;
  }
  .max-h-\[80dvh\] {
    max-height: 80dvh;
  }
  .min-h-\[60vh\] {
    min-height: 60vh;
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-150 {
    width: calc(var(--spacing) * 150);
  }
  .w-\[90vw\] {
    width: 90vw;
  }
  .w-full {
    width: 100%;
  }
  .container-fluid {
    max-width: 1650px;
    padding-inline: calc(var(--spacing) * 40);
  }
  .max-w-\[720px\] {
    max-width: 720px;
  }
  .min-w-\[60vw\] {
    min-width: 60vw;
  }
  .origin-center {
    transform-origin: center;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-rotate-90 {
    rotate: calc(90deg * -1);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .gap-15 {
    gap: calc(var(--spacing) * 15);
  }
  .gap-20 {
    gap: calc(var(--spacing) * 20);
  }
  .gap-30 {
    gap: calc(var(--spacing) * 30);
  }
  .space-y-10 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-30 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 30) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 30) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-40 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 40) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 40) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-30 {
    column-gap: calc(var(--spacing) * 30);
  }
  .gap-y-30 {
    row-gap: calc(var(--spacing) * 30);
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-\[2px\] {
    border-radius: 2px;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-\[1px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l-\[1px\] {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-black\/20 {
    border-color: color-mix(in srgb, #072252 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }
  .border-white {
    border-color: var(--color-white);
  }
  .bg-\[\#ef4444\] {
    background-color: #ef4444;
  }
  .bg-\[\#fc0303\] {
    background-color: #fc0303;
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/10 {
    background-color: color-mix(in srgb, #072252 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .custom-gradient {
    background-image: linear-gradient(282deg,var(--color-gradient-end) 0%,var(--color-gradient-start) 124.73%);
  }
  .from-black\/40 {
    --tw-gradient-from: color-mix(in srgb, #072252 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-black\/20 {
    --tw-gradient-via: color-mix(in srgb, #072252 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-25\% {
    --tw-gradient-via-position: 25%;
  }
  .mask-right {
    mask-position: right;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-\[10px\] {
    padding: 10px;
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-50 {
    padding-inline: calc(var(--spacing) * 50);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-15 {
    padding-block: calc(var(--spacing) * 15);
  }
  .py-30 {
    padding-block: calc(var(--spacing) * 30);
  }
  .py-32 {
    padding-block: calc(var(--spacing) * 32);
  }
  .py-40 {
    padding-block: calc(var(--spacing) * 40);
  }
  .py-100 {
    padding-block: calc(var(--spacing) * 100);
  }
  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }
  .pt-120 {
    padding-top: calc(var(--spacing) * 120);
  }
  .pr-30 {
    padding-right: calc(var(--spacing) * 30);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pb-30 {
    padding-bottom: calc(var(--spacing) * 30);
  }
  .pb-50 {
    padding-bottom: calc(var(--spacing) * 50);
  }
  .pb-70 {
    padding-bottom: calc(var(--spacing) * 70);
  }
  .pb-90 {
    padding-bottom: calc(var(--spacing) * 90);
  }
  .pl-30 {
    padding-left: calc(var(--spacing) * 30);
  }
  .text-center {
    text-align: center;
  }
  .text-right {
    text-align: right;
  }
  .text-18ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 18px;
    --tw-leading: 0.89;
    line-height: 0.89;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: 0.056em;
    letter-spacing: 0.056em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 18px;
    }
    @media (width >= 992px) {
      font-size: 18px;
    }
    @media (width >= 1280px) {
      font-size: 18px;
    }
  }
  .text-20ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 18px;
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: 0.05em;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 20px;
    }
    @media (width >= 992px) {
      font-size: 20px;
    }
    @media (width >= 1280px) {
      font-size: 20px;
    }
  }
  .text-22ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 20px;
    --tw-leading: 0.85;
    line-height: 0.85;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: 0.09em;
    letter-spacing: 0.09em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 22px;
    }
    @media (width >= 992px) {
      font-size: 22px;
    }
    @media (width >= 1280px) {
      font-size: 22px;
    }
  }
  .text-30ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 26px;
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.065em;
    letter-spacing: -0.065em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 27px;
    }
    @media (width >= 992px) {
      font-size: 27px;
    }
    @media (width >= 1280px) {
      font-size: 30px;
    }
  }
  .text-140ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 42px;
    --tw-leading: 0.71;
    line-height: 0.71;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.021em;
    letter-spacing: -0.021em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 84px;
    }
    @media (width >= 992px) {
      font-size: 98px;
    }
    @media (width >= 1280px) {
      font-size: 140px;
    }
  }
  .text-250ncb {
    font-family: var(--font-nitti-grotesk-condensed);
    font-size: 140px;
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.004em;
    letter-spacing: -0.004em;
    text-transform: uppercase;
    @media (width >= 576px) {
      font-size: 100px;
    }
    @media (width >= 992px) {
      font-size: 175px;
    }
    @media (width >= 1280px) {
      font-size: 250px;
    }
  }
  .text-24nr {
    font-family: var(--font-nitti-grotesk);
    font-size: 22px;
    --tw-leading: 1.41;
    line-height: 1.41;
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    --tw-tracking: -0.123em;
    letter-spacing: -0.123em;
    @media (width >= 576px) {
      font-size: 22px;
    }
    @media (width >= 992px) {
      font-size: 24px;
    }
    @media (width >= 1280px) {
      font-size: 24px;
    }
  }
  .text-32nm {
    font-family: var(--font-nitti-grotesk);
    font-size: 26px;
    --tw-leading: 1.23;
    line-height: 1.23;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    --tw-tracking: -0.092em;
    letter-spacing: -0.092em;
    @media (width >= 576px) {
      font-size: 29px;
    }
    @media (width >= 992px) {
      font-size: 29px;
    }
    @media (width >= 1280px) {
      font-size: 32px;
    }
  }
  .text-36nb {
    font-family: var(--font-nitti-grotesk);
    font-size: 29px;
    --tw-leading: 0.86;
    line-height: 0.86;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.11em;
    letter-spacing: -0.11em;
    @media (width >= 576px) {
      font-size: 33px;
    }
    @media (width >= 992px) {
      font-size: 33px;
    }
    @media (width >= 1280px) {
      font-size: 36px;
    }
  }
  .text-46nb {
    font-family: var(--font-nitti-grotesk);
    font-size: 33px;
    --tw-leading: 1.06;
    line-height: 1.06;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.085em;
    letter-spacing: -0.085em;
    @media (width >= 576px) {
      font-size: 42px;
    }
    @media (width >= 992px) {
      font-size: 42px;
    }
    @media (width >= 1280px) {
      font-size: 46px;
    }
  }
  .text-56nb {
    font-family: var(--font-nitti-grotesk);
    font-size: 31px;
    --tw-leading: 1.06;
    line-height: 1.06;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.071em;
    letter-spacing: -0.071em;
    @media (width >= 576px) {
      font-size: 45px;
    }
    @media (width >= 992px) {
      font-size: 51px;
    }
    @media (width >= 1280px) {
      font-size: 56px;
    }
  }
  .text-68nb {
    font-family: var(--font-nitti-grotesk);
    font-size: 38px;
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    --tw-tracking: -0.058em;
    letter-spacing: -0.058em;
    @media (width >= 576px) {
      font-size: 55px;
    }
    @media (width >= 992px) {
      font-size: 62px;
    }
    @media (width >= 1280px) {
      font-size: 68px;
    }
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-\[12px\] {
    font-size: 12px;
  }
  .text-\[14px\] {
    font-size: 14px;
  }
  .leading-\[1\] {
    --tw-leading: 1;
    line-height: 1;
  }
  .tracking-\[2px\] {
    --tw-tracking: 2px;
    letter-spacing: 2px;
  }
  .text-\[\#80878A\] {
    color: #80878A;
  }
  .text-\[\#ffffff\] {
    color: #ffffff;
  }
  .text-blue {
    color: var(--color-blue);
  }
  .text-inherit {
    color: inherit;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow {
    color: var(--color-yellow);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .not-italic {
    font-style: normal;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .mix-blend-difference {
    mix-blend-mode: difference;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .backface-hidden {
    backface-visibility: hidden;
  }
  .group-hover\:scale-125 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 125%;
        --tw-scale-y: 125%;
        --tw-scale-z: 125%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:text-yellow {
    &:hover {
      @media (hover: hover) {
        color: var(--color-yellow);
      }
    }
  }
  .aria-selected\:bg-black {
    &[aria-selected="true"] {
      background-color: var(--color-black);
    }
  }
  .aria-selected\:text-white {
    &[aria-selected="true"] {
      color: var(--color-white);
    }
  }
  .max-lg\:min-h-\[50dvh\] {
    @media (width < 992px) {
      min-height: 50dvh;
    }
  }
  .md\:order-1 {
    @media (width >= 768px) {
      order: 1;
    }
  }
  .md\:order-2 {
    @media (width >= 768px) {
      order: 2;
    }
  }
  .md\:col-span-6 {
    @media (width >= 768px) {
      grid-column: span 6 / span 6;
    }
  }
  .md\:my-80 {
    @media (width >= 768px) {
      margin-block: calc(var(--spacing) * 80);
    }
  }
  .md\:mb-50 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 50);
    }
  }
  .md\:mb-80 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 80);
    }
  }
  .md\:w-10\/12 {
    @media (width >= 768px) {
      width: calc(10/12 * 100%);
    }
  }
  .md\:gap-30 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 30);
    }
  }
  .md\:space-y-50 {
    @media (width >= 768px) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 50) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 50) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:gap-x-50 {
    @media (width >= 768px) {
      column-gap: calc(var(--spacing) * 50);
    }
  }
  .md\:py-30 {
    @media (width >= 768px) {
      padding-block: calc(var(--spacing) * 30);
    }
  }
  .lg\:col-span-9 {
    @media (width >= 992px) {
      grid-column: span 9 / span 9;
    }
  }
  .lg\:col-span-10 {
    @media (width >= 992px) {
      grid-column: span 10 / span 10;
    }
  }
  .lg\:col-start-2 {
    @media (width >= 992px) {
      grid-column-start: 2;
    }
  }
  .lg\:aspect-\[1440\/450\] {
    @media (width >= 992px) {
      aspect-ratio: 1440/450;
    }
  }
  .lg\:w-10\/12 {
    @media (width >= 992px) {
      width: calc(10/12 * 100%);
    }
  }
  .lg\:w-\[670px\] {
    @media (width >= 992px) {
      width: 670px;
    }
  }
  .lg\:pt-120 {
    @media (width >= 992px) {
      padding-top: calc(var(--spacing) * 120);
    }
  }
  .lg\:pb-50 {
    @media (width >= 992px) {
      padding-bottom: calc(var(--spacing) * 50);
    }
  }
  .xl\:col-span-8 {
    @media (width >= 1280px) {
      grid-column: span 8 / span 8;
    }
  }
  .xl\:col-start-3 {
    @media (width >= 1280px) {
      grid-column-start: 3;
    }
  }
  .xl\:w-8\/12 {
    @media (width >= 1280px) {
      width: calc(8/12 * 100%);
    }
  }
  .xl\:w-10\/12 {
    @media (width >= 1280px) {
      width: calc(10/12 * 100%);
    }
  }
  .xl\:space-y-80 {
    @media (width >= 1280px) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 80) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 80) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .xl\:gap-x-70 {
    @media (width >= 1280px) {
      column-gap: calc(var(--spacing) * 70);
    }
  }
  .\32 xl\:w-8\/10 {
    @media (width >= 1536px) {
      width: calc(8/10 * 100%);
    }
  }
  .\[\&_\.current\]\:bg-black {
    & .current {
      background-color: var(--color-black);
    }
  }
  .\[\&_\.current\]\:text-white {
    & .current {
      color: var(--color-white);
    }
  }
  .\[\&_\.screen-reader-text\]\:hidden {
    & .screen-reader-text {
      display: none;
    }
  }
  .\[\&_\.search-field\]\:w-full {
    & .search-field {
      width: 100%;
    }
  }
  .\[\&_\.search-field\]\:bg-\[transparent\] {
    & .search-field {
      background-color: transparent;
    }
  }
  .\[\&_\.search-field\]\:py-20 {
    & .search-field {
      padding-block: calc(var(--spacing) * 20);
    }
  }
  .\[\&_\.search-field\]\:pr-100 {
    & .search-field {
      padding-right: calc(var(--spacing) * 100);
    }
  }
  .\[\&_\.search-field\]\:pl-25 {
    & .search-field {
      padding-left: calc(var(--spacing) * 25);
    }
  }
  .\[\&_\.search-field\]\:placeholder-black {
    & .search-field {
      &::placeholder {
        color: var(--color-black);
      }
    }
  }
  .\[\&_\.search-field\]\:outline-none {
    & .search-field {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .\[\&_\.search-submit\]\:absolute {
    & .search-submit {
      position: absolute;
    }
  }
  .\[\&_\.search-submit\]\:top-0 {
    & .search-submit {
      top: calc(var(--spacing) * 0);
    }
  }
  .\[\&_\.search-submit\]\:right-0 {
    & .search-submit {
      right: calc(var(--spacing) * 0);
    }
  }
  .\[\&_\.search-submit\]\:block {
    & .search-submit {
      display: block;
    }
  }
  .\[\&_\.search-submit\]\:h-full {
    & .search-submit {
      height: 100%;
    }
  }
  .\[\&_\.search-submit\]\:w-100 {
    & .search-submit {
      width: calc(var(--spacing) * 100);
    }
  }
  .\[\&_\.search-submit\]\:cursor-pointer {
    & .search-submit {
      cursor: pointer;
    }
  }
  .\[\&_\.search-submit\]\:bg-search-dark {
    & .search-submit {
      background-image: url("../img/icons/search--dark.svg");
    }
  }
  .\[\&_\.search-submit\]\:bg-center {
    & .search-submit {
      background-position: center;
    }
  }
  .\[\&_\.search-submit\]\:bg-no-repeat {
    & .search-submit {
      background-repeat: no-repeat;
    }
  }
  .\[\&_\.search-submit\]\:indent-\[9999px\] {
    & .search-submit {
      text-indent: 9999px;
    }
  }
  .hover\:\[\&_a\]\:bg-black {
    &:hover {
      @media (hover: hover) {
        & a {
          background-color: var(--color-black);
        }
      }
    }
  }
  .hover\:\[\&_a\]\:text-white {
    &:hover {
      @media (hover: hover) {
        & a {
          color: var(--color-white);
        }
      }
    }
  }
  .\[\&_form\]\:relative {
    & form {
      position: relative;
    }
  }
  .\[\&_svg\]\:h-auto {
    & svg {
      height: auto;
    }
  }
  .\[\&_svg\]\:w-100 {
    & svg {
      width: calc(var(--spacing) * 100);
    }
  }
  .\[\&_svg\]\:mix-blend-difference {
    & svg {
      mix-blend-mode: difference;
    }
  }
  .\[\&_svg_rect\]\:mix-blend-difference {
    & svg rect {
      mix-blend-mode: difference;
    }
  }
}
.container {
  margin-inline: auto;
  padding-inline: calc(var(--spacing) * 30);
  @media (width < 576px) {
    max-width: none;
  }
  @media (width >= 576px) {
    padding-inline: calc(var(--spacing) * 40);
  }
}
:root {
  --twcb-scrollbar-width: 0px;
}
.prose {
  max-width: none;
}
h1, h2, h3, h4, h5, h6 {
  text-wrap: pretty;
}
.prose h2 {
  margin-bottom: calc(var(--spacing) * 30);
  font-family: var(--font-nitti-grotesk);
  font-size: 38px;
  --tw-leading: 1;
  line-height: 1;
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
  --tw-tracking: -0.058em;
  letter-spacing: -0.058em;
  @media (width >= 576px) {
    font-size: 55px;
  }
  @media (width >= 992px) {
    font-size: 62px;
  }
  @media (width >= 1280px) {
    font-size: 68px;
  }
  color: var(--color-blue);
}
.prose p.highlight {
  margin-top: calc(var(--spacing) * 0);
  margin-bottom: calc(var(--spacing) * 23);
  font-family: var(--font-nitti-grotesk);
  font-size: 33px;
  --tw-leading: 1.06;
  line-height: 1.06;
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
  --tw-tracking: -0.085em;
  letter-spacing: -0.085em;
  @media (width >= 576px) {
    font-size: 42px;
  }
  @media (width >= 992px) {
    font-size: 42px;
  }
  @media (width >= 1280px) {
    font-size: 46px;
  }
  color: var(--color-blue);
}
.prose p.intro {
  margin-top: calc(var(--spacing) * 0);
  margin-bottom: calc(var(--spacing) * 23);
  font-family: var(--font-nitti-grotesk);
  font-size: 26px;
  --tw-leading: 1.23;
  line-height: 1.23;
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  --tw-tracking: -0.092em;
  letter-spacing: -0.092em;
  @media (width >= 576px) {
    font-size: 29px;
  }
  @media (width >= 992px) {
    font-size: 29px;
  }
  @media (width >= 1280px) {
    font-size: 32px;
  }
}
.prose p:not(.intro):not(.highlight), .prose a:not(.button), .prose li {
  margin-top: calc(var(--spacing) * 16);
  margin-bottom: calc(var(--spacing) * 16);
  font-family: var(--font-nitti-grotesk);
  font-size: 22px;
  --tw-leading: 1.41;
  line-height: 1.41;
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  --tw-tracking: -0.123em;
  letter-spacing: -0.123em;
  @media (width >= 576px) {
    font-size: 22px;
  }
  @media (width >= 992px) {
    font-size: 24px;
  }
  @media (width >= 1280px) {
    font-size: 24px;
  }
}
.prose a:not(.button) {
  color: var(--color-blue);
  text-decoration-line: none;
}
.button {
  display: inline-block;
  cursor: pointer;
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-black);
  padding-inline: calc(var(--spacing) * 25);
  padding-block: calc(var(--spacing) * 15);
  text-align: center;
  text-decoration-line: none;
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: clip;
  color: var(--color-black);
}
#page-wrap {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
}
#content-wrap {
  flex: 1;
}
[data-hidden-on-load] {
  opacity: 0;
}
html.lenis, html.lenis body {
  height: auto;
}
.lenis.lenis-smooth {
  scroll-behavior: auto !important;
}
.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}
.lenis.lenis-stopped {
  overflow: hidden;
}
.lenis.lenis-smooth iframe {
  pointer-events: none;
}
@media (scripting: enabled) and (prefers-reduced-motion: no-preference) {
  [twk-aos] {
    opacity: 0%;
  }
}
[data-block="text"][data-next-block="text"] {
  margin-bottom: calc(var(--spacing) * 36);
}
[data-block="text"][data-prev-block="text"] {
  margin-top: calc(var(--spacing) * 36);
}
.switch {
  display: inline-block;
  height: 34px;
  position: relative;
  width: 60px;
}
.switch input {
  display: none;
}
.slider {
  background-color: #ccc;
  bottom: 0;
  cursor: pointer;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: .4s;
}
.slider:before {
  background-color: #fff;
  bottom: 4px;
  content: "";
  height: 26px;
  left: 4px;
  position: absolute;
  transition: .4s;
  width: 26px;
}
input:checked + .slider {
  background-color: black;
}
input:checked + .slider:before {
  transform: translateX(26px);
}
.slider.round {
  border-radius: 34px;
}
.slider.round:before {
  border-radius: 50%;
}
.responsive-table {
  overflow: auto;
}
.gallery[class*='gallery-columns-'] {
  display: grid;
  gap: calc(var(--spacing) * 20);
}
.gallery.gallery-columns-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.gallery.gallery-columns-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.gallery.gallery-columns-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.gallery.gallery-columns-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.gallery.gallery-columns-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.gallery.gallery-columns-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.gallery.gallery-columns-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.gallery.gallery-columns-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.gallery.gallery-columns-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}
#page-wrap .gallery .gallery-item {
  position: relative;
  float: none;
  aspect-ratio: 20/15;
  height: auto;
  width: 100%;
}
#page-wrap .gallery .gallery-item picture, #page-wrap .gallery .gallery-item img {
  position: absolute;
  inset: calc(var(--spacing) * 0);
  height: 100%;
  width: 100%;
  object-fit: cover;
  &>* {
    position: absolute;
  }
  &>* {
    inset: calc(var(--spacing) * 0);
  }
  &>* {
    height: 100%;
  }
  &>* {
    width: 100%;
  }
  &>* {
    object-fit: cover;
  }
  &:is(*) {
    position: absolute;
  }
  &:is(*) {
    inset: calc(var(--spacing) * 0);
  }
  &:is(*) {
    height: 100%;
  }
  &:is(*) {
    width: 100%;
  }
  &:is(*) {
    object-fit: cover;
  }
  --tw-border-style: none;
  border-style: none;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-content: "";
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}
