import gsap from 'gsap';

function getCookie(name) {
	var dc = document.cookie;
	var prefix = name + "=";
	var begin = dc.indexOf("; " + prefix);
	if (begin == -1) {
		begin = dc.indexOf(prefix);
		if (begin != 0) return null;
	}
	else
	{
		begin += 2;
		var end = document.cookie.indexOf(";", begin);
		if (end == -1) {
		end = dc.length;
		}
	}
	// because unescape has been deprecated, replaced with decodeURI
	//return unescape(dc.substring(begin + prefix.length, end));
	return decodeURI(dc.substring(begin + prefix.length, end));
}
function setCookie(cname, cvalue, exdays) {
	var d = new Date();
	d.setTime(d.getTime() + (exdays*24*60*60*1000));
	var expires = "expires="+ d.toUTCString();

	if ( exdays == 0 ){
		document.cookie = cname + "=" + cvalue + ";path=/";
	} else {
		document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
	}
}

export default function notifications() {
	let cookieName = php_vars.notificationCookieName;     // Gets the cookie name.
	if ( ! cookieName || cookieName == '' ){
		cookieName = 'twk_cookies_popup_00001'
	}
    let isThereCookie = getCookie(cookieName);            // Checks if the message was already displayed and closed (in that case there will be a cookie set).
    let cookieExpire = php_vars.notificationCookieExpire; // Gets the cookie expiration time.

	//console.log('Cookie name: ' + cookieName);
	//console.log('Is there a cookie: ' + isThereCookie);
	//console.log('Cookie Expire: ' + cookieExpire);

	const notificationPopup = document.querySelector( '[data-name="notification-popup"]' );

	// We show the popup if the user has not already closed it before.	
	if ( ! isThereCookie && notificationPopup ){
		gsap.to( notificationPopup, { autoAlpha: 1, display: 'block', duration: 0.5 });
	}

	// Popup close button: Closes the popup and sets the cookie to prevent it from showing again.
	const notificationCloseButton = document.querySelector( '[data-name="notification-popup-close-button"]' )
	notificationCloseButton?.addEventListener('click', function(event) {
		event.preventDefault();

		setCookie( cookieName, 'true', cookieExpire);  // Once its shown, it won't show up again until the cookie expires.

		gsap.to( notificationPopup, { autoAlpha: 0, display: 'none', duration: 0.5 });
	})

	// Popup link: Closes the notification popup and goes to the link. We set the cookie to prevent the popup from showing again.
	const notificationContentLink = document.querySelector( '[data-name="notification-popup"] .notification-popup-link' )
	notificationContentLink?.addEventListener('click', function(event) {
		event.preventDefault();

		setCookie( cookieName, 'true', cookieExpire);

		window.location.href = this.getAttribute("href");
	})
}