/* "Lock" the screen */
:root{
	--twcb-scrollbar-width: 0px; /* scrollbar width */
}

@utility container-fluid{
	@apply max-w-[1650px] px-40;
}

@utility scroll-lock {
	@apply overflow-hidden h-screen;
	width: calc((100% - var(--twcb-scrollbar-width)));
}
@utility scroll-lock-viewport {
	@apply overflow-hidden h-screen;
	width: calc((100vw - var(--twcb-scrollbar-width)));
}

/* Images */
@utility fill-image{
	@apply absolute inset-0 w-full h-full object-cover;
	@apply [&>*]:absolute [&>*]:inset-0 [&>*]:w-full [&>*]:h-full [&>*]:object-cover;
	@apply [*]:absolute [*]:inset-0 [*]:w-full [*]:h-full [*]:object-cover;
}

@utility bg-search-light {
	background-image: url("../img/icons/search--light.svg");
}
@utility bg-search-dark {
	background-image: url("../img/icons/search--dark.svg");
}


/* Misc */
@utility stretched-link {
	@apply after:absolute after:inset-0 after:z-1 after:pointer-events-auto after:bg-transparent;
}

/* Gradient */
@utility custom-gradient{
	@apply bg-[linear-gradient(282deg,var(--color-gradient-end)_0%,var(--color-gradient-start)_124.73%)];
}

/* Overlay - gradient */
@utility overlay{
	@apply absolute z-1 pointer-events-none;
}
@utility overlay--full{
	@apply inset-0 bg-black/25;
}
@utility overlay--top{
	@apply top-0 left-0 right-0 h-full bg-gradient-to-b from-black/40 via-black/20 via-25%;
}
@utility overlay--bottom{
	@apply inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0)_66.45%,rgba(0,0,0,0.4)_99.85%)];
}
@utility overlay--bottom-darker{
	@apply bottom-0 left-0 right-0 h-full bg-gradient-to-t from-black/60 via-black/20 via-25%;
}
