function bannerChooseTheRightVideo(){
	const isMobile = window.innerWidth < 768;	
	
	if ( isMobile ) {
		// Play the mobile video and stop the desktop video in case it was playing.
		const desktopVideo = document.querySelector("[data-name='banner-desktop-video']")
		if ( desktopVideo ) {
			desktopVideo.pause()
			desktopVideo.style.display = 'none';
		}
		
		const mobileVideo = document.querySelector("[data-name='banner-mobile-video']")
		if (  mobileVideo ) {
			mobileVideo.play()
			mobileVideo.style.display = 'block'
		}
	} else {
		// Play the desktop video and stop the mobile video in case it was playing.
		const mobileVideo = document.querySelector("[data-name='banner-mobile-video']")
		if (  mobileVideo ) {
			mobileVideo.pause()
			mobileVideo.style.display = 'none'
		}

		const desktopVideo = document.querySelector("[data-name='banner-desktop-video']")
		if ( desktopVideo ) {
			desktopVideo.play()
			desktopVideo.style.display = 'block'
		}
	}
}

function bannerVideo(){
	bannerChooseTheRightVideo();

	window.addEventListener('resize', bannerChooseTheRightVideo);
}

export { bannerVideo };