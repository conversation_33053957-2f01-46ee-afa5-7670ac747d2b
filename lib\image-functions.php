<?php
/**
 * Available custom function for the images.
 *
 * @package twkmedia
 */

/**
 * Get an image with some fallbacks.
 *
 * @param array   $image An image from ACF.
 * @param string  $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string  $class Classes we want to add to the image HTML tag.
 * @param string  $fallback Field name of the image you want to fall back to. Global settings -> Images (pages, archive, blog, posts, search).
 * @param boolean $featured True if you want to fallback first to the featured image. False if you only want the global image fallback.
 * @return string HTML image tag or null if no featured image is set or null if no image (it should be one at least on global settings).
 */
function twk_output_acf_image_with_fallback( $image, $size = 'large', $class = '', $fallback = 'page', $lazy='lazy', $featured = false ) {
	$fallback_option = $fallback . '_fallback_image';

	if ( twk_output_acf_image( $image, $size, $class, $lazy ) ) {
		return twk_output_acf_image( $image, $size, $class, $lazy );
	} elseif ( twk_output_featured_image( null, $size, $class, $lazy ) && $featured ) {
		return twk_output_featured_image( null, $size, $class, $lazy );
	} elseif ( twk_output_acf_image( get_field( $fallback_option, 'option' ), $size, $class, $lazy ) ) {
		return twk_output_acf_image( get_field( $fallback_option, 'option' ), $size, $class, $lazy );
	} else {
		return null;
	}
}


/**
 * Get the featured image with fallback.
 *
 * @param integer $post_id Id of the post.
 * @param string  $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string  $class Classes we want to add to the image HTML tag.
 * @param string  $fallback Field name of the image you want to fall back to. Global settings -> Images (pages, archive, blog, posts, search).
 * @return string HTML image tag or null if no featured image is set or null if no image (it should be one at least on global settings).
 */
function twk_output_featured_image_with_fallback( $post_id = null, $size = 'large', $class = '', $lazy='lazy', $fallback = 'page' ) {
	$fallback_option = $fallback . '_fallback_image';

	if ( twk_output_featured_image( $post_id, $size, $class, $lazy ) ) {
		return twk_output_featured_image( $post_id, $size, $class, $lazy );
	} elseif ( twk_output_acf_image( get_field( $fallback_option, 'option' ), $size, $class, $lazy ) ) {
		return twk_output_acf_image( get_field( $fallback_option, 'option' ), $size, $class, $lazy );
	} else {
		return null;
	}
}




/**
 * Get the featured image.
 *
 * @param integer $post_id Id of the post.
 * @param string  $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string  $class Classes we want to add to the image HTML tag.
 * @param string  $lazy The loading attribute for lazy loading the image. Ex. Lazy or eager.
 * @return string HTML image tag or null if no featured image is set or null if no featured image is set.
 */
function twk_output_featured_image( $post_id = null, $size = 'large', $class = '', $lazy = 'lazy' ) {
	$img_size         = $size;
	$image_src        = wp_get_attachment_image_src( get_post_thumbnail_id( $post_id ), $img_size, true );
	$img_srcset       = wp_get_attachment_image_srcset( get_post_thumbnail_id( $post_id ), $img_size );
	$img_srcset_sizes = wp_get_attachment_image_sizes( get_post_thumbnail_id( $post_id ), $img_size );
	$img_alt          = get_post_meta( get_post_thumbnail_id( $post_id ), '_wp_attachment_image_alt', true );

	$image_role = 'img';
	if ( $img_alt === '' ) {
		$image_role = 'presentation';
	}

	if ( ! $lazy ){
		$lazy = 'lazy';
	}

	// think if you need custom sizes eg, are any images not full width on mobile.

	if ( $image_src[0] && strpos( $image_src[0], 'wp-includes/images/media/default' ) === false ) {
		return '<img loading="' . $lazy . '" role="' . $image_role . '" src="' . $image_src[0] . '" alt="' . $img_alt . '" srcset="' . $img_srcset . '" sizes="' . $img_srcset_sizes . '" class="' . $class . '"/>';
	} else {
		return null;
	}
}


/**
 * Output and image tag with ACF.
 *
 * @param array  $image An image from ACF.
 * @param string $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string $class Classes we want to add to the image HTML tag.
 * @param string $lazy The loading attribute for lazy loading the image.
 * @return string HTML image tag or null if no image is set.
 */
function twk_output_acf_image( $image, $size = 'large', $class = '', $lazy = 'lazy' ) {
	if ( ! isset( $image['ID'] ) ) {
		return null;
	}

	if ( ! $lazy ){
		$lazy = 'lazy';
	}

	$img_size         = $size;
	$image_src        = wp_get_attachment_image_src( $image['ID'], $img_size, true );
	$img_srcset       = wp_get_attachment_image_srcset( $image['ID'], $img_size );
	$img_srcset_sizes = wp_get_attachment_image_sizes( $image['ID'], $img_size );
	$img_alt          = get_post_meta( $image['ID'], '_wp_attachment_image_alt', true );

	$image_role = 'img';
	if ( $img_alt === '' ) {
		$image_role = 'presentation';
	}

	if ( $image_src[0] && strpos( $image_src[0], 'wp-includes/images/media/default' ) === false ) {
		return '<img loading="' . $lazy . '" role="' . $image_role . '" src="' . $image_src[0] . '" alt="' . $img_alt . '" srcset="' . $img_srcset . '" sizes="' . $img_srcset_sizes . '" class="' . $class . '"/>';
	} else {
		return null;
	}
}


/**
 * Get image HTML by ID
 *
 * @param integer $image_id ID of the image.
 * @param string  $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string  $class Classes we want to add to the image HTML tag.
 * @param string  $lazy The loading attribute for lazy loading the image.
 * @return string HTML image tag.
 */
function twk_output_id_image( $image_id, $size = 'large', $class = '', $lazy = 'lazy' ) {
	$img_size         = $size;
	$image_src        = wp_get_attachment_image_src( $image_id, $img_size, true );
	$img_srcset       = wp_get_attachment_image_srcset( $image_id, $img_size );
	$img_srcset_sizes = wp_get_attachment_image_sizes( $image_id, $img_size );
	$img_alt          = get_post_meta( $image_id, '_wp_attachment_image_alt', true );

	$image_role = 'img';
	if ( $img_alt === '' ) {
		$image_role = 'presentation';
	}

	if ( ! $lazy ){
		$lazy = 'lazy';
	}

	return '<img loading="' . $lazy . '" role="' . $image_role . '" src="' . $image_src[0] . '" alt="' . $img_alt . '" srcset="' . $img_srcset . '" sizes="' . $img_srcset_sizes . '" class="' . $class . '"/>';
}


/**
 * Get a responsive image.
 *
 * @param array  $image An image from ACF.
 * @param string $size Size of the image we want to get: thumbnail, medium, large, full or a custom size.
 * @param string $class Classes we want to add to the image HTML tag.
 * @param string $lazy The loading attribute for lazy loading the image.
 * @return string HTML image tag.
 */
function twk_output_acf_image_responsive( $image, $size = 'large', $class = '', $lazy = 'lazy' ) {
	$img_size         = $size;
	$image_src        = wp_get_attachment_image_src( $image['ID'], $img_size, true );
	$img_srcset       = wp_get_attachment_image_srcset( $image['ID'], $img_size );
	$img_srcset_sizes = wp_get_attachment_image_sizes( $image['ID'], $img_size );
	$img_alt          = get_post_meta( $image['ID'], '_wp_attachment_image_alt', true );

	$aspect_ratio = ( $image_src['2'] / $image_src['1'] ) * 100;

	$image_role = 'img';
	if ( $img_alt === '' ) {
		$image_role = 'presentation';
	}

	if ( ! $lazy ){
		$lazy = 'lazy';
	}

	return '<div class="' . $class . '"><div class="responsive-img" style="padding-bottom: ' . $aspect_ratio . '%"><img loading="' . $lazy . '" role="' . $image_role . '" src="' . $image_src[0] . '" alt="' . $img_alt . '" srcset="' . $img_srcset . '" sizes="' . $img_srcset_sizes . '" /></div></div>';
}



/*
*
* ADDITIONAL FUNCTIONS
*
*/


/**
 * Gets the image url with a fallback system.
 *
 * @param integer $post_id The post ID.
 * @param string  $size The size of the image that will be returned.
 * @param string  $fallback The fallback image on the options setting page.
 * @return String $image_url Image url.
 */
function twk_get_image_url_with_fallback( $post_id = null, $size = 'large', $fallback = 'page' ) {

	// Get the data.
	if ( null === $post_id ) {
		$post_id = get_the_ID();
	}

	$fallback_option = $fallback . '_fallback_image';

	if ( get_field( 'banner_image', $post_id ) ){
		if ( 'full' === $size ) {
			$image_url = get_field( 'banner_image', $post_id )['url'];               // banner image as first option.
		} else {
			$image_url = get_field( 'banner_image', $post_id )['sizes'][ $size ];    // banner image as first option.
		}
	}

	if ( ! isset( $image_url ) && get_the_post_thumbnail_url( $post_id, $size ) ) :
		$image_url = get_the_post_thumbnail_url( $post_id, $size );               // thumbnail as second option.
	endif;

	if ( ! isset( $image_url ) ) :
		if ( 'full' === $size ) {
			$image_url = get_field( $fallback_option, 'option' )['url'];             // fallback on the options page as last option.
		} else {
			$image_url = get_field( $fallback_option, 'option' )['sizes'][ $size ];  // fallback on the options page as last option.
		}
	endif;

	return $image_url;
}


/* Remove EXIF metadata from images */
function remove_exif_metadata_from_images($file) {
  // Ensure the file exists
  if (!file_exists($file['file'])) {
      return $file;
  }

  // Check if the file is a JPEG image
  $file_type = wp_check_filetype($file['file']);
  if ($file_type['ext'] === 'jpg' || $file_type['ext'] === 'jpeg') {
      $image_path = $file['file'];

      // Attempt to create an image resource from the uploaded file
      $image = @imagecreatefromjpeg($image_path);
      if ($image !== false) {
          // Re-save the image to strip EXIF metadata
          $quality = apply_filters('jpeg_quality', 80, $image_path); // Allow quality adjustments via filter
          imagejpeg($image, $image_path, $quality); // Save with specified quality
          imagedestroy($image); // Free memory
      } else {
          error_log('Failed to create image resource for EXIF stripping: ' . $image_path);
      }
  }

  return $file;
}
add_filter('wp_handle_upload', 'remove_exif_metadata_from_images', 10, 1);