import Hls from "hls.js";

function lazyLoadVideos (){
	// To use this the video src should be empty and the data-src attribute should have the video url.
	const videos = document.querySelectorAll('[data-name="video-lazy-load"]');

	if ( videos.length > 0 ) {
		videos.forEach(video => {
			const source   = video.querySelector('source')
			const videoUrl = source.getAttribute('data-src')

			video.addEventListener('canplay', () => {
				video.play();
			});

			// Set source when video is near viewport
			const observer = new IntersectionObserver((entries) => {
				if (entries[0].isIntersecting) {
					source.setAttribute('src', videoUrl);
					video.load();
					observer.disconnect();
				}
			});
			
			observer.observe(video);
		});
	}
}

function adaptiveVideo() {
    let videos = document.querySelectorAll('[data-name="adaptive-video"]');

	if ( videos.length > 0 ) {
		
		videos.forEach(video => {
			const videoSrc = video.getAttribute('data-source');
			if ( ! videoSrc) return;
		
			if ( Hls.isSupported() ) {
				const hls = new Hls({
					debug: false,
					// Optional: Set an initial starting level if needed
					startLevel: -1, // -1 lets hls.js automatically select the best level
					// Optional: Cap the maximum quality level based on conditions
					autoLevelCapping: -1, // -1 means no capping, adjust based on your needs
				});
		
				hls.loadSource(videoSrc);
				hls.attachMedia(video);
		
				hls.on(Hls.Events.MEDIA_ATTACHED, function () {
					video.muted = true;
					video.play();
				});
		
				hls.on(Hls.Events.LEVEL_SWITCHED, function (event, data) {
					//console.log('Quality level switched to:', data.level);
				});
		
				hls.on(Hls.Events.ERROR, function(event, data) {
					//console.error('HLS error:', data);
					// Additional error handling logic can be implemented here
				});
			} else if (video.canPlayType('application/vnd.apple.mpegurl')) {
				video.src = videoSrc;
				video.addEventListener('canplay', function () {
					video.muted = true;
					video.play();
				});
				video.addEventListener('error', function () {
					console.error('Video playback error');
					// Additional fallback logic can be implemented here
				});
			} else {
				console.error('HLS not supported and no native HLS support available.');
			}
		});
	}
}

function video(){
	lazyLoadVideos();
	adaptiveVideo();
}

export { video };