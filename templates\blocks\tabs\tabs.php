<?php
/**
 * Template part to display the block tabs.
 *
 * @package twkmedia
 */

/**
 * Variables like $tab_navigation_number and $tab_navigation_first_number are used because this block can be used multiple times on a page.
 * We want to make sure that the tab navigation is unique for each instance of the block and no ID is repeated on the page.
 */
?>

<div data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="container">
	<?php if ( get_sub_field( 'title' ) || have_rows( 'tabs' ) ) : ?>
		<div class="flex flex-wrap justify-center">
			<div class="w-full md:w-10/12">
				<?php if ( get_sub_field( 'title' ) ) : ?>
					<h2 class="mb-30"><?php echo get_sub_field( 'title' ); ?></h2>
				<?php endif; ?>

				<div data-name="tabs-container">
					<!-- Tabs navigation -->
					<?php if ( have_rows( 'tabs' ) ) : ?>
						<div role="tablist" aria-label="tabs">
							<?php
							if ( ! isset( $tab_navigation_number ) ) :
								$tab_navigation_number       = 0;
								$tab_navigation_first_number = 1;
							else :
								$tab_navigation_first_number = $tab_navigation_number + 1;
							endif;

							while ( have_rows( 'tabs' ) ) :
								the_row();
								$tab_navigation_number++;
								?>
						
								<?php if ( get_sub_field( 'title' ) ) : ?>
									<button
										data-name="<?php echo esc_attr( get_sub_field( 'twk_settings_tab_custom_id' ) ? sanitize_title( get_sub_field( 'twk_settings_tab_custom_id' ), '-' ) : 'tab-' . absint( $tab_navigation_number ) ); ?>"
										id="tab-<?php echo $tab_navigation_number; ?>"
										role="tab"
										aria-selected="<?php echo ( $tab_navigation_number === $tab_navigation_first_number ? 'true' : 'false' ); ?>"
										aria-controls="panel-<?php echo $tab_navigation_number; ?>"
										tabindex="<?php echo ( $tab_navigation_number === $tab_navigation_first_number ? '0' : '-1' ); ?>"
										class="uppercase border-b border-black/20 px-10 pt-10 pb-20 mb-20 aria-selected:bg-black aria-selected:text-white"
										>
										<?php echo get_sub_field( 'title' ); ?>
									</button>
								<?php endif; ?>
						
								<?php
							endwhile;
							?>
						</div>
					<?php endif; ?>

					<!-- Tabs content -->
					<?php if ( have_rows( 'tabs' ) ) : ?>
						<?php
						if ( ! isset( $tab_content_number ) ) :
							$tab_content_number       = 0;
							$tab_content_first_number = 1;
						else :
							$tab_content_first_number = $tab_content_number + 1;
						endif;

						while ( have_rows( 'tabs' ) ) :
							the_row();
							$tab_content_number++;
							?>
							<div
								id="panel-<?php echo $tab_content_number; ?>"
								role="tabpanel"
								aria-labelledby="tab-<?php echo $tab_content_number; ?>"
								<?php echo ( $tab_content_number === $tab_content_first_number ? '' : 'hidden' ); ?>
								>

								<?php if ( get_sub_field( 'text' ) ) : ?>
									<div class="prose">
										<?php echo get_sub_field( 'text' ); ?>
									</div>
								<?php endif; ?>

							</div>
							<?php
						endwhile;
						?>
					<?php endif; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>
</div>