<?php
/**
 * Template part to display a notification popup
 *
 * @package twkmedia
 */

if ( get_field( 'popup_active', 'option' ) ) : ?>
	<div
		data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>"
		data-name="notification-popup"
		class="fixed z-50 top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 border py-30 pl-30 bg-white w-[90vw] max-w-[720px]"
		style="display: none;"
		>
		<div class="prose max-h-[70vh] overflow-auto pr-30">
			<?php if ( get_field( 'popup_text', 'option' ) ) : ?>
				<div><?php echo get_field( 'popup_text', 'option' ); ?></div>
			<?php endif; ?>

			<?php
			if ( get_field( 'popup_link', 'option' ) ) :
				echo twk_compose_acf_link( get_field( 'popup_link', 'option' ), 'notification-popup-link button mt-30' );
			endif;
			?>
		</div>

		<div data-name="notification-popup-close-button" class="fixed top-10 right-10 cursor-pointer">
			<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
				<g fill="none" stroke="#000000" stroke-linecap="square">
					<path d="M0.5,0.5 L23.127417,23.127417"/>
					<path d="M0.5,0.5 L23.127417,23.127417" transform="matrix(-1 0 0 1 23.627 0)"/>
				</g>
			</svg>
		</div>
	</div>

	<?php
endif;
