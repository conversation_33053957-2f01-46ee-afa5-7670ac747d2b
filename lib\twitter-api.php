<?php
/**
 * Twitter API
 *
 * @package twkmedia
 */

/**
 * Build the base string
 *
 * @param [type] $base_uri
 * @param [type] $method
 * @param [type] $params
 * @return void
 */
function build_base_string( $base_uri, $method, $params ) {
	$r = array();
	ksort( $params );

	foreach ( $params as $key => $value ) {
		$r[] = "$key=" . rawurlencode( $value );
	}

	return $method . '&' . rawurlencode( $base_uri ) . '&' . rawurlencode( implode( '&', $r ) );
}


/**
 * Build authorization header.
 *
 * @param [type] $oauth
 * @return void
 */
function build_authorization_header( $oauth ) {
	$r      = 'Authorization: OAuth ';
	$values = array();

	foreach ( $oauth as $key => $value )
		$values[] = "$key=\"" . rawurlencode($value) . "\"";

	$r .= implode( ', ', $values );

	return $r;
}

/**
 * Return Tweet.
 *
 * @param [type] $twitter_feed
 * @param [type] $count
 * @return void
 */
function return_tweet( $twitter_feed, $count ) {
	$oauth_access_token        = '83593613-njgkCQW5f3YO8foFURxFhnPXiSxrRrEzOh6FF2UjO'; // REPLACE.
	$oauth_access_token_secret = 'IKwYCqP04CaDdg0STEKcmzbqaoYKwRiknGu047wkprN6s';      // REPLACE.
	$consumer_key              = 'QPzkHAsHvYEwpsJfaOuaKXtZ4';                          // REPLACE.
	$consumer_secret           = 'rVWvgJaqKjclHMAGHeTdw4jvwLbpRuxGVcM4IHEGXdvMM4Nzpy'; // REPLACE.

	$request = array(
		'screen_name' => 'USERNAME', // REPLACE.
		'count'       => $count,
		'tweet_mode'  => 'extended',
	);

	$oauth = array(
		'oauth_consumer_key'     => $consumer_key,
		'oauth_nonce'            => time(),
		'oauth_signature_method' => 'HMAC-SHA1',
		'oauth_token'            => $oauth_access_token,
		'oauth_timestamp'        => time(),
		'oauth_version'          => '1.0',
	);

	$oauth                    = array_merge( $oauth, $request );
	$base_info                = build_base_string( "https://api.twitter.com/1.1/$twitter_feed.json", 'GET', $oauth );
	$composite_key            = rawurlencode( $consumer_secret ) . '&' . rawurlencode( $oauth_access_token_secret );
	$oauth_signature          = base64_encode( hash_hmac( 'sha1', $base_info, $composite_key, true ) );
	$oauth['oauth_signature'] = $oauth_signature;
	$header                   = array(
		build_authorization_header( $oauth ),
		'Expect:',
	);
	$options                  = array(
		CURLOPT_HTTPHEADER     => $header,
		CURLOPT_HEADER         => false,
		CURLOPT_URL            => "https://api.twitter.com/1.1/$twitter_feed.json?" . http_build_query( $request ),
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_SSL_VERIFYPEER => false,
	);

	$feed = curl_init();
	curl_setopt_array( $feed, $options );

	$json = curl_exec( $feed );
	curl_close( $feed );

	return json_decode( $json, true );
}

/**
 * Trim tweet
 *
 * @param String $trim Tweet.
 * @return $trim
 */
function trim_tweet( $trim ) {
	$url  = '/(https?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:\/?#[\]@!\$&\'\(\)\*\+,;=.%]+/m';
	$trim = preg_replace( $url, "<a target=\"_blank\" href=\"$0\">$0</a>", $trim );
	$trim = preg_replace( "/#([A-Za-z0-9\/\.]*)/", "<a target=\"_new\" href=\"http://twitter.com/search?q=$1\">#$1</a>", $trim );
	$trim = preg_replace( "/@([A-Za-z0-9_\/\.]*)/", "<a href=\"http://www.twitter.com/$1\">@$1</a>", $trim );

	return $trim;
}



/**
 * Turn twitter time stamp to human redeable time
 * (i.e. '3 days ago')
 *
 * @param [type] $datetime
 * @param boolean $full
 * @return void
 */
function time_elapsed_string( $datetime, $full = false ) {
	$now  = new DateTime();
	$ago  = new DateTime( $datetime );
	$diff = $now->diff( $ago );

	$diff->w  = floor( $diff->d / 7 );
	$diff->d -= $diff->w * 7;

	$string = array(
		'y' => 'year',
		'm' => 'month',
		'w' => 'week',
		'd' => 'day',
		'h' => 'hour',
		'i' => 'minute',
		's' => 'second',
	);
	foreach ( $string as $k => &$v ) {
		if ( $diff->$k ) {
			$v = $diff->$k . ' ' . $v . ( $diff->$k > 1 ? 's' : '' );
		} else {
			unset( $string[ $k ] );
		}
	}

	if ( ! $full ) $string = array_slice( $string, 0, 1 );

	return $string ? implode( ', ', $string ) . ' ago' : 'just now';
}


// -----------------------------------------------
// Use the following code to display twitter posts:
// -----------------------------------------------
/*
$tweets = return_tweet('statuses/user_timeline', '1'); // change number for amount of tweets you want to display.

if ( ! isset( $tweets['errors'] ) ) :
	foreach ( $tweets as $tweet ) {
		echo '<p>' . trim_tweet( $tweet['full_text']) . '</p>';
	}
endif;
*/

