<?php
/**
 * All custom post and taxonomy registerations.
 *
 * @package twkmedia
 */

// Custom post types goes here.






/**
 * Changes WP default behaviour.
 * By default WP does not show any results in archives page if your taxonomy is only associated with a custom post type.
 *
 * @param [type] $query
 * @return void
 */
function dw_handle_posts( $query ) {
	if ( ! $query->is_main_query() || is_admin() ) {
		return;
	}

	if ( $query->is_tax ) {
		$post_type = get_query_var( 'post_type' );

		if ( ! $post_type ) {
			global $wp_taxonomies;

			$taxo      = get_queried_object();
			$post_type = ( isset( $wp_taxonomies[ $taxo->taxonomy ] ) ) ? $wp_taxonomies[ $taxo->taxonomy ]->object_type : array();

			$query->set( 'post_type', $post_type );
		}
	}

	return $query;
}
//add_action( 'pre_get_posts', 'dw_handle_posts' );
