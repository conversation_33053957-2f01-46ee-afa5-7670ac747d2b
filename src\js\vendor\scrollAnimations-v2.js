import gsap from 'gsap'
import ScrollTrigger from 'gsap/ScrollTrigger'
import { twkAnimations, gsapEffects } from './scrollAnimations-v2--effects';

gsap.registerPlugin(ScrollTrigger);


export default function() {
	twkAnimations() // Gsap custom effects to add to timelines.

	let onFirstBlock = false;

	let mm = gsap.matchMedia()

	// Make sure all the breakpoints are here and there is no extra breakpoint that can break the code.
	const screenXS  = 340;
	const screenSM  = 576;
	const screenMD  = 768;
	const screenLG  = 992;
	const screenXL  = 1280;
	const screen2XL = 1536;

	mm.add(
		{
			// set up any number of arbitrarily-named conditions. The function below will be called when ANY of them match.
			isXS: `(min-width: ${screenXS - 1}px)`,
			isSM: `(min-width: ${screenSM - 1}px)`,
			isMD: `(min-width: ${screenMD - 1}px)`,
			isLG: `(min-width: ${screenLG - 1}px)`,
			isXL: `(min-width: ${screenXL - 1}px)`,
			is2XL: `(min-width: ${screen2XL - 1}px)`,
			reduceMotion: "(prefers-reduced-motion: reduce)",
		},
		( context ) => {
			// context.conditions has a boolean property for each condition defined above indicating if it's matched or not.
			let { isXS, isSM, isMD, isLG, isXL, is2XL, reduceMotion  } = context.conditions;

			if ( reduceMotion ) {
				console.info('Reduced motion is ON');
			} else {
				let sections = gsap.utils.toArray('[twk-aos]');

				sections.forEach(section => {
					if ( is2XL && section.getAttribute('twk-aos-2xl') ) {
						getSectionAnimation( section, '2xl' );
					} else if ( isXL && section.getAttribute('twk-aos-xl') ) {
						getSectionAnimation( section, 'xl' );
					} else if ( isLG && section.getAttribute('twk-aos-lg') ) {
						getSectionAnimation( section, 'lg' );
					} else if ( isMD && section.getAttribute('twk-aos-md') ) {
						getSectionAnimation( section,'md' );
					} else if ( isSM && section.getAttribute('twk-aos-sm') ) {
						getSectionAnimation( section,'sm' );
					} else if ( isXS && section.getAttribute('twk-aos-xs') ) {
						getSectionAnimation( section, 'xs' );
					} else {
						getSectionAnimation( section );
					}
				});
			}



			return () => {
				// optionally return a cleanup function that will be called when none of the conditions match anymore (after having matched)
				// it'll automatically call context.revert() - do NOT do that here . Only put custom cleanup code here.
			};
		}
	);




	// Create an Observer instance to check if the page changes in height
	// and refresh ScrollTrigger so the markers are in the correct position.
	const resizeObserver = new ResizeObserver( (entries) => {
			ScrollTrigger.sort();
			ScrollTrigger.refresh();
		}
	)
	resizeObserver.observe( document.body )




	function getSectionAnimation( section, screenSize = '' ) {
		// Check if the section is the first block on the main container.
		// We want it to animate earlier than the others.
		if ( section.closest('section') !== null ){
			const parent         = document.querySelector('main.main');
			const firstSectionId = parent?.firstElementChild?.id

			if ( section.closest('section').id === firstSectionId ) {
				onFirstBlock = true;
			} else {
				onFirstBlock = false;
			}
		}

		section.setAttribute('twk-aos-status', 'animate');  // Marks that js is loaded and the block is ready to be animated.

		// Defaults
		let twkAosTrigger   = ( onFirstBlock ? 'top bottom+=20px' : 'top bottom-=150px' );
		let twkAosDuration  = 0.8;
		let twkAosDelay     = '';
		let twkAosMarker    = false;
		let twkAosMarkerId  = 'section';
		let twkAosIsStagger = false;
		let twkAosStagger   = 0.2;
		let twkAosEase      = 'power1.out';

		if ( section.getAttribute('twk-aos-' + screenSize + '-trigger') !== null ){
			twkAosTrigger = section.getAttribute('twk-aos-' + screenSize + '-trigger');
		} else if( section.getAttribute('twk-aos-trigger') !== null ) {
			twkAosTrigger = section.getAttribute('twk-aos-trigger');
		}
		if ( section.getAttribute('twk-aos-' + screenSize + '-duration') !== null ){
			twkAosDuration = section.getAttribute('twk-aos-' + screenSize + '-duration') / 1000; // From html we get them in milliseconds
		} else if( section.getAttribute('twk-aos-duration') !== null ) {
			twkAosDuration = section.getAttribute('twk-aos-duration') / 1000;                    // From html we get them in milliseconds
		}
		if ( section.getAttribute('twk-aos-' + screenSize + '-delay') !== null ){
			twkAosDelay = section.getAttribute('twk-aos-' + screenSize + '-delay') / 1000;       // From html we get them in milliseconds
		} else if( section.getAttribute('twk-aos-delay') !== null ) {
			twkAosDelay = section.getAttribute('twk-aos-delay') / 1000;                          // From html we get them in milliseconds
		}
		if ( section.getAttribute('twk-aos-marker') !== null ){
			twkAosMarker  = true;
			twkAosMarkerId = section.getAttribute('twk-aos-marker');
		}
		if ( section.getAttribute('twk-aos-stagger') !== null ){
			twkAosIsStagger = true;
			twkAosStagger = section.getAttribute('twk-aos-stagger') / 1000;                      // From html we get them in milliseconds
		}
		if (section.getAttribute('twk-aos-ease') !== null) {
			twkAosEase = section.getAttribute('twk-aos-ease')
		}


		// The timeline.
		if ( section ) {
			
			/* // Log data.
			console.table({
				'twk-aos-trigger' : { 'Value': twkAosTrigger },
				'twk-aos-duration' : {'Value': twkAosDuration },
				'twk-aos-delay' : {'Value': twkAosDelay },
				'twk-aos-marker' : {'Value': twkAosMarker },
				'HTML' : {'Value': section }
			}); */
			
			
			let tl = gsap.timeline({
				defaults: {
					duration: twkAosDuration,
					ease: twkAosEase,
				},
				scrollTrigger: {
					trigger: section,
					fastScrollEnd: true,
					start: `${twkAosTrigger}`,
					end: "bottom 80%",
					id: twkAosMarkerId,
					markers: twkAosMarker,
					invalidateOnRefresh: true,
					//toggleActions: "play none none none",
					once: true,
					onLeave: () => {
						//console.log('onLeave');
						section.setAttribute('twk-aos-status', 'animated');
					},
					onEnter: () => {
						//console.log('onEnter');
						section.setAttribute('twk-aos-status', 'animating');
					},
				}
			});
			
			// Call ScrollTrigger.refresh() after initialization
			ScrollTrigger.refresh();
			ScrollTrigger.sort();

			// Add a window load event listener to ensure the setup is correct after page load
			window.addEventListener('load', () => {
				ScrollTrigger.refresh();
			});



			const animationName =  ( screenSize != '' ? section.getAttribute('twk-aos-' + screenSize) : section.getAttribute('twk-aos') ) 
		
			// Get the defined effects from the scrollAnimations-v2--effects.js file.
			// Create the gsap tl functions for each effect dynamically.
			// Run the right tl effect.
			const animationNames = [];
			gsapEffects?.forEach(effect => {
				animationNames.push(effect.id);
			});

			const animationFunctions = {};
			animationNames.forEach(name => {
				animationFunctions[name] = (target, config) => tl[name](target, config);
			});
			
			

			if ( twkAosIsStagger ) {
				let sectionConfig = {
					duration: twkAosDuration,
					delay: twkAosDelay,
					ease: twkAosEase,
					stagger: twkAosStagger,
				}

				if ( animationName in animationFunctions ) {
					animationFunctions[animationName](section, { duration: 0 });  // reset the animations on the parent element or the whole content will be hidden.
					
					const allChildren = section.children;
					const allChildrenArray = Array.from(allChildren);
					const filteredChildren = allChildrenArray.filter(child => !child.hasAttribute('twk-aos-ignore'));
					
					animationFunctions[animationName](filteredChildren, sectionConfig);
				} else {
					console.warn("Invalid animationName: " + animationName);
				}
			} else {
				let sectionConfig = {
					duration: twkAosDuration,
					delay: twkAosDelay,
					ease: twkAosEase,
				}

				if ( animationName in animationFunctions ) {
					animationFunctions[animationName](section, sectionConfig);
				} else {
					console.warn("Invalid animationName: " + animationName);
				}
			}
			
		} else {
			console.error('Section element not found. Please check your selector.');
		}
	}
}
