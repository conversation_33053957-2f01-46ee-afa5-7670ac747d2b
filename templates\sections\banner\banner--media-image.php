<?php
/**
 * Template part for displaying the page banner media image.
 *
 * @package twkmedia
 */

$image_url = twk_get_image_url_with_fallback( $post->ID, 'full' );
?>

<section data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" data-name="banner-media-image">
	<div class="relative aspect-[14/12] lg:aspect-[1440/450] flex flex-col justify-center items-center">
		<?php echo twk_output_acf_image_with_fallback( get_field( 'banner_image' ), 'full', 'fill-image', 'page', 'eager', true ); ?>
		
		<div class="container text-center relative z-10">
			<div class="grid grid-cols-12 gap-15 md:gap-30">
				<div class="col-span-10 col-start-2 text-center my-50">
	
					<h1 class="">
						<?php if ( get_field( 'banner_alternative_title' ) ) : ?>
							<?php echo get_field( 'banner_alternative_title' ); ?>
						<?php else : ?>
							<?php the_title(); ?>
						<?php endif; ?>
					</h1>
	
					<?php if ( get_field( 'banner_intro_text' ) ) : ?>
						<p class=""><?php echo get_field( 'banner_intro_text' ); ?></p>
					<?php endif; ?>
	
					<?php if ( get_post_type() === 'post' ) : ?>
						<p class=""><?php echo get_the_date(); ?></p>
					<?php endif; ?>
	
				</div>
			</div>
		</div>

		<!-- Overlays -->
		<?php $overlay = get_field('twk_settings_banner_overlay'); ?>

		<?php if ( in_array( 'top', $overlay ) ) : ?>
			<div class="overlay overlay--top"></div>
		<?php endif; ?>

		<?php if ( in_array( 'bottom', $overlay ) ) : ?>
			<div class="overlay overlay--bottom"></div>
		<?php endif; ?>

		<?php if ( in_array( 'full', $overlay ) ) : ?>
			<div class="overlay overlay--full"></div>
		<?php endif; ?>
		<!-- END Overlays -->
	</div>
</section>

