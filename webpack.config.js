const path = require('path');
const WebpackBuildNotifierPlugin = require('webpack-build-notifier');

module.exports = (env, argv) => {
    const { mode } = argv;
	const isDev = env.dev === true;

    return {
        mode: isDev ? 'development' : mode,
        watch: true,
        devtool: mode === 'development' ? 'source-map' : false,
        externals: {
            jquery: "jQuery"
        },
        entry: {
            main: isDev ? './src/js/script--dev.js' : './src/js/script.js'
        },
        output: {
            filename: isDev ? 'bundle--dev.js' : 'bundle.js',
            path: path.resolve(__dirname, 'assets/js'),
        },
        plugins: [
            new WebpackBuildNotifierPlugin(),
        ],
        module: {
            rules: [
                {
                    test: /\.js$/,
                    exclude: /node_modules/,
                    loader: 'babel-loader'
                },
				{
                    test: /\.css$/,
                    use: ['style-loader', 'css-loader']
                }
            ]
        },
        node: {
            __filename: true,
            __dirname: true
        },
        resolve: {
            alias: {
                '@': path.resolve(__dirname, ''),
                '@js': path.resolve(__dirname, 'src/js'),
            }
        }
    }
};