function calculateSize(){
	const iframeVideos = document.querySelectorAll('[data-name="iframe-video"]')

	if ( iframeVideos.length > 0 ) {
		iframeVideos.forEach(video => {
			const blockWidth = video.parentNode.offsetWidth
			const blockHeight = video.parentNode.offsetHeight

			video.style.width = blockWidth * 3 + 'px'
			video.style.height = blockHeight * 3 + 'px'
		})
	}
}


export default function(){
	calculateSize()

	window.addEventListener('resize', calculateSize)
}