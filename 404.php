<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package twkmedia
 */

get_header();
?>

<?php if ( get_field( '404_banner_image', 'option' ) ) : ?>
	<section class="banner banner--404 relative aspect-[10/2]">
		<?php echo twk_output_acf_image( get_field( '404_banner_image', 'option' ), 'large', 'fill-image', 'eager' ); ?>
	</section>
<?php endif; ?>

<main data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" id="main" class="main">
	<section class="my-80">
		<div class="grid grid-cols-12">
			<div class="col-span-8 col-start-3">

				<h1 class="">
					<?php echo ( get_field( '404_alternative_title', 'option' ) ? get_field( '404_alternative_title', 'option' ) : 'Not found <small>(404 error page)</small>' ); ?>
				</h1>

				<?php if ( get_field( '404_text', 'option' ) ) : ?>
					<p class=""><?php echo get_field( '404_text', 'option' ); ?></p>
				<?php endif; ?>

				<?php if ( get_field( '404_link', 'option' ) ) : ?>
					<?php echo twk_compose_acf_link( get_field( '404_link', 'option' ), 'button my-20' ); ?>
				<?php endif; ?>

			</div>
		</div>
	</section>
</main>

<?php
get_footer();
