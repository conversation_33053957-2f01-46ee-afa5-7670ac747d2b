/* Admin styles. */

/*
	Menu bar
*/
#wp-admin-bar-wp-logo > .ab-item{
	pointer-events: none;
}


/*
	At a glance widget
*/
#dashboard_right_now .code-snippet-count a:before {
    content: "\f475";
}


/*
	Show a field only for the TWK media team
*/
body:not(.user-id-1) .twk-only{
	display: none;
}


/*
	ACF meta fields
*/
.acf-field.meta {
    background: #EEEEEE;
}

/*
	Smaller wysiwyg editor
*/
.xs .acf-editor-wrap iframe{
	max-height: 80px;
	overflow-y: auto;
}
.sm .acf-editor-wrap iframe{
	max-height: 120px;
	overflow-y: auto;
}


/*
	ACF Radio Colors
*/

/* primary : <span class="color-choice color-choice--primary" title="Primary">Primary</span> */
.color-choice {
    display: inline-block;
    width: 20px;
    height: 20px;
    font-size: 0;
	margin: 0px 5px 5px;
	border: 1px solid transparent;
}
.color-choice--transparent{ 
	background-color: transparent;
	border-color: black;
}

/* left : <span class="alignment-choice alignment-choice--left" title="Left">Left</span> */
.alignment-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;
}
.alignment-choice--left{
	background: url('../img/admin/align-left.svg') no-repeat;
}
.alignment-choice--right{
	background: url('../img/admin/align-right.svg') no-repeat;
}
.alignment-choice--center{
	background: url('../img/admin/align-center.svg') no-repeat;
}

/* video : <span class="media-choice media-choice--video" title="Video">Video</span> */
.media-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;
}
.media-choice--picture{
	background: url('../img/admin/picture.svg') no-repeat;
}
.media-choice--video{
	background: url('../img/admin/video.svg') no-repeat;
}

/* full : <span class="border-choice border-choice--full" title="Full">Full</span> */
.border-choice{
	display: inline-block;
    vertical-align: middle;
	width: 24px;
	height: 24px;
    font-size: 0;
}
.border-choice--full{
	background: url('../img/admin/border-full.svg') no-repeat;
}
.border-choice--center{
	background: url('../img/admin/border-center.svg') no-repeat;
}
.border-choice--top{
	background: url('../img/admin/border-top.svg') no-repeat;
}
.border-choice--bottom{
	background: url('../img/admin/border-bottom.svg') no-repeat;
}



/*
	ACF icons
	- Social media
	- API Keys
*/
.acf-field.icon-x .acf-label label::before,
.acf-field.icon-bluesky .acf-label label::before,
.acf-field.icon-facebook .acf-label label::before,
.acf-field.icon-instagram .acf-label label::before,
.acf-field.icon-linkedin .acf-label label::before,
.acf-field.icon-youtube .acf-label label::before,
.acf-field.icon-tiktok .acf-label label::before,
.acf-field.icon-pinterest .acf-label label::before,
.acf-field.icon-threads .acf-label label::before,
.acf-field.icon-google-maps .acf-label label::before {
	content: '';
    display: inline-block;
	height: 20px;
    width: 20px;
    margin-right: 10px;
}
.acf-field.icon-x .acf-label label::before { background: url('../img/social/x-icon.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-bluesky .acf-label label::before { background: url('../img/social/bluesky-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-facebook .acf-label label::before { background: url('../img/social/facebook-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-instagram .acf-label label::before { background: url('../img/social/instagram-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-linkedin .acf-label label::before { background: url('../img/social/linkedin-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-youtube .acf-label label::before { background: url('../img/social/youtube-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-tiktok .acf-label label::before { background: url('../img/social/tiktok-icon.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-pinterest .acf-label label::before { background: url('../img/social/pinterest-icon--color.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-threads .acf-label label::before { background: url('../img/social/threads-icon.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }
.acf-field.icon-google-maps .acf-label label::before { background: url('../img/icons/google-maps.svg') no-repeat; background-position: 0 0; background-size: 20px 20px; }

.acf-field.icon-x label,
.acf-field.icon-bluesky label,
.acf-field.icon-facebook label,
.acf-field.icon-instagram label,
.acf-field.icon-linkedin label,
.acf-field.icon-youtube label,
.acf-field.icon-tiktok label,
.acf-field.icon-pinterest label,
.acf-field.icon-threads label,
.acf-field.icon-google-maps label {
    display: flex !important;
    align-items: center;
}
