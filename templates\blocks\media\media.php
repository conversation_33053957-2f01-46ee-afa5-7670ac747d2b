<?php
/**
 * Template part to display the media block.
 *
 * @package twkmedia
 */

if ( get_sub_field( 'twk_settings_size' ) === 'container' ) :
	$media_container_class        = 'flex flex-wrap justify-center';
	$media_container_inside_class = 'w-full md:w-10/12';
else :
	$media_container_class        = '';
	$media_container_inside_class = '';
endif;
?>

<div data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="<?php echo ( get_sub_field( 'twk_settings_size' ) === 'container' ? 'container' : '' ); ?>">
	<?php if ( get_sub_field( 'title' ) ) : ?>
		<div class="flex flex-wrap justify-center">
			<div class="w-full md:w-10/12">
				<h2 class="mb-25"><?php echo get_sub_field( 'title' ); ?></h2>
			</div>
		</div>
	<?php endif; ?>

	<?php if ( get_sub_field( 'twk_settings_media_type' ) === 'image' ) : ?>
		<div class="<?php echo $media_container_class; ?>">
			<div class="<?php echo $media_container_inside_class; ?>">
				<div class="relative aspect-video">
					<?php echo twk_output_acf_image_with_fallback( get_sub_field( 'cover_image' ), 'full', 'fill-image', get_post_type() ); ?>
				</div>
			</div>
		</div>
	<?php elseif ( get_sub_field( 'twk_settings_media_type' ) === 'video_file' || get_sub_field( 'twk_settings_media_type' ) === 'video_url' ) : ?>
		<?php
		if ( get_sub_field( 'twk_settings_media_type' ) === 'video_file' && get_sub_field( 'video_file' ) ) :
			$video_url  = get_sub_field( 'video_file' )['url'];
			$video_mime = get_sub_field( 'video_file' )['mime_type'];
		elseif ( get_sub_field( 'twk_settings_media_type' ) === 'video_url' && get_sub_field( 'video_url' ) ) :
			$video_url  = get_sub_field( 'video_url' );
			$video_mime = 'video/mp4';
		endif;
		?>
		<div class="<?php echo $media_container_class; ?>">
			<div class="<?php echo $media_container_inside_class; ?>">
				<div data-name="video" class="group relative aspect-video">
					<?php if ( strpos( $video_url, '.m3u8' ) !== false ) : ?>
						<!-- Adaptive video -->
						<video
							data-name="adaptive-video"
							data-source="<?php echo $video_url; ?>"
							<?php echo( get_sub_field( 'cover_image' ) ? 'poster="' . get_sub_field( 'cover_image' )['url'] . '"' : '' ); ?>
							autoplay muted loop playsinline
							class="fill-image"
							>
						</video>
					<?php else : ?>
						<!-- HTML Video (file or mp4 link) -->
						<video
							data-name="video-lazy-load"
							<?php echo( get_sub_field( 'cover_image' ) ? 'poster="' . get_sub_field( 'cover_image' )['url'] . '"' : '' ); ?>
							autoplay muted loop playsinline
							class="fill-image"
							>
							<source data-src="<?php echo $video_url; ?>" src="" type="<?php echo $video_mime; ?>">
							Your browser does not support HTML5 video.
						</video>
					<?php endif; ?>

					<?php if ( get_sub_field( 'video_full_url' ) ) : ?>
						<div data-name="video-cursor" class="absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 children:size-50 children:lg:size-100">
							<?php require locate_template( 'assets/img/icons/play.svg' ); ?>
						</div>
						<a href="<?php echo get_sub_field( 'video_full_url' ); ?>" data-fancybox class="stretched-link"><span class="sr-only">Watch full video</span></a>
					<?php endif; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>
</div>
