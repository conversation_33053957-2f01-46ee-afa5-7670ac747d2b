<?php
/**
 * Template part to display the social media links.
 *
 * @package twkmedia
 */

$social_media = array(
	'x' => array(
		'field' => 'x_url',
		'icon'  => 'x-stroke--white.svg',
	),
	'bluesky' => array(
		'field' => 'bluesky_url',
		'icon'  => 'bluesky-stroke--white.svg',
	),
	'facebook' => array(
		'field' => 'facebook_url',
		'icon'  => 'facebook-stroke--white.svg',
	),
	'instagram' => array(
		'field' => 'instagram_url',
		'icon'  => 'instagram-stroke--white.svg',
	),
	'linkedin' => array(
		'field' => 'linkedin_url',
		'icon'  => 'linkedin-stroke--white.svg',
	),
	'youtube' => array(
		'field' => 'youtube_url',
		'icon'  => 'youtube-stroke--white.svg',
	),
	'tiktok' => array(
		'field' => 'tiktok_url',
		'icon'  => 'tiktok-stroke--white.svg',
	),
	'pinterest' => array(
		'field' => 'pinterest_url',
		'icon'  => 'pinterest-stroke--white.svg',
	),
	'threads' => array(
		'field' => 'threads_url',
		'icon'  => 'threads-stroke--white.svg',
	),
);

if ( count( $social_media ) > 0 ) :
	?>
		<nav data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" class="flex flex-wrap gap-10">
			<?php
			foreach ( $social_media as $platform => $data ) :
				if ( get_field( $data['field'], 'option' ) ) :
					?>
					<a
						href="<?php echo esc_url( get_field( $data['field'], 'option' ) ); ?>"
						class="group relative children:size-50"
						aria-label="<?php echo 'Link to our ' . esc_attr( str_replace( '_url', '', $data['field'] ) ) . ' page'; ?>"
						>
						<?php require locate_template( "assets/img/social/{$data['icon']}" ); ?>
					</a>
					<?php
				endif;
			endforeach;
			?>
		</nav>
	<?php
endif;