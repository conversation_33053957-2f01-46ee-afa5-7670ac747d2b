<?php
/**
 * Template part for displaying the page banner media video.
 *
 * @package twkmedia
 */

$image_url = twk_get_image_url_with_fallback( $post->ID, 'full' );
?>

<section data-filename="<?php echo str_replace(get_template_directory() . '/', '', __FILE__); ?>" data-name="banner-media-video">

	<?php $poster = get_field( 'banner_image' ); ?>

	<div class="relative aspect-[10/12] lg:aspect-[1440/450] flex flex-col justify-center items-center">
		<?php
		if ( get_field( 'twk_settings_video_type' ) === 'file' ) {
			if ( get_field( 'banner_video_desktop' ) ) {
				$video_desktop_url  = get_field( 'banner_video_desktop' )['url'];
				$video_desktop_mime = get_field( 'banner_video_desktop' )['mime_type'];
			}
			if ( get_field( 'banner_video_mobile' ) ) {
				$video_mobile_url  = get_field( 'banner_video_mobile' )['url'];
				$video_mobile_mime = get_field( 'banner_video_mobile' )['mime_type'];
			}
		} elseif ( get_field( 'twk_settings_video_type' ) === 'url' ) {
			$video_url  = get_field( 'banner_video_url' );
			$video_mime = 'video/mp4';
		}
		?>

		<?php if ( get_field( 'twk_settings_video_type' ) === 'file' ) : ?>
			<!-- Desktop and mobile Video -->
			<video
				poster="<?php echo ( isset( $poster['url'] ) ? $poster['url'] : '' ); ?>"
				preload="none" muted loop playsinline
				data-name="banner-desktop-video" class="fill-image">
				<source src="<?php echo $video_desktop_url; ?>" type="<?php echo $video_desktop_mime; ?>">
				Your browser does not support the video tag.
			</video>
			<video
				poster="<?php echo ( isset( $poster['url'] ) ? $poster['url'] : '' ); ?>"
				preload="none" muted loop playsinline
				data-name="banner-mobile-video" class="fill-image">
				<source src="<?php echo $video_mobile_url; ?>" type="<?php echo $video_mobile_mime; ?>">
				Your browser does not support the video tag.
			</video>
		<?php elseif ( get_field( 'twk_settings_video_type' ) === 'url' ) : ?>
			<?php if ( strpos( $video_url, '.m3u8' ) !== false ) : ?>
				<!-- Adaptive video -->
				<video
					data-name="adaptive-video"
					data-source="<?php echo $video_url; ?>"
					<?php echo( isset( $poster['url'] ) ? 'poster="' . $poster['url'] . '"' : '' ); ?>
					autoplay muted loop playsinline
					class="fill-image">
				</video>
			<?php else : ?>
				<!-- HTML Video (file or mp4 link) -->
				<video
					<?php echo( isset( $poster['url'] ) ? 'poster="' . $poster['url'] . '"' : '' ); ?>
					autoplay muted loop playsinline
					class="fill-image"
					>
					<source src="<?php echo $video_url; ?>" type="<?php echo $video_mime; ?>">
					Your browser does not support HTML5 video.
				</video>
			<?php endif; ?>
		<?php endif; ?>

		

		<div class="container text-white text-center relative z-10">
			<div class="flex flex-wrap justify-center text-center">
				<div class="w-full lg:w-10/12 xl:w-8/12 py-15">
	
					<h1 class="">
						<?php if ( get_field( 'banner_alternative_title' ) ) : ?>
							<?php echo get_field( 'banner_alternative_title' ); ?>
						<?php else : ?>
							<?php the_title(); ?>
						<?php endif; ?>
					</h1>
	
					<?php if ( get_field( 'banner_intro_text' ) ) : ?>
						<p class=""><?php echo get_field( 'banner_intro_text' ); ?></p>
					<?php endif; ?>
	
					<?php if ( get_post_type() === 'post' ) : ?>
						<p class=""><?php echo get_the_date(); ?></p>
					<?php endif; ?>
	
				</div>
			</div>
		</div>
	
		<!-- Overlays -->
		<?php $overlay = get_field('twk_settings_banner_overlay'); ?>
	
		<?php if ( in_array( 'top', $overlay ) ) : ?>
			<div class="overlay overlay--top"></div>
		<?php endif; ?>
	
		<?php if ( in_array( 'bottom', $overlay ) ) : ?>
			<div class="overlay overlay--bottom"></div>
		<?php endif; ?>
	
		<?php if ( in_array( 'full', $overlay ) ) : ?>
			<div class="overlay overlay--full"></div>
		<?php endif; ?>
		<!-- END Overlays -->
	</div>

</section>
