@import 'magnific-popup/dist/magnific-popup.css';


/**
 * Fade-zoom animation for first dialog
 */

/* start state */
.my-mfp-zoom-in .mfp-content {
	opacity: 0;
	transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
	transform: scale(0.8);
}

/* animate in */
.my-mfp-zoom-in.mfp-ready .mfp-content {
	opacity: 1;
	transform: scale(1);
}

/* animate out */
.my-mfp-zoom-in.mfp-removing .mfp-content {
	transform: scale(0.8);
	opacity: 0;
}

/* Dark overlay, start state */
.my-mfp-zoom-in.mfp-bg {
	opacity: 0;
	transition: opacity 0.3s ease-out;
}
/* animate in */
.my-mfp-zoom-in.mfp-ready.mfp-bg {
	opacity: 0.8;
}
/* animate out */
.my-mfp-zoom-in.mfp-removing.mfp-bg {
	opacity: 0;
}
