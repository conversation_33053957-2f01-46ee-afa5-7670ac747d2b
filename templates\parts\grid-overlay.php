<button class="grid-overlay-button fixed z-[9999] top-0 right-65 py-4 px-8 text-white text-[12px] leading-[1] bg-black border-white border-b-[1px] border-l-[1px]">Grid</button>

<div id="grid-overlay" class="fixed inset-0 w-full h-full pointer-events-none z-[999] hidden">
    <div class="container grid grid-cols-12 gap-20 h-full">
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
        <div class="col-span-1 bg-black/10"></div>
    </div>
</div>

<script>
    window.addEventListener('load', function() {
        function gridOverlay() {
            const gridButton = document.querySelector('.grid-overlay-button');
            const gridOverlay = document.getElementById('grid-overlay');

            // Function to toggle grid visibility
            const toggleGrid = () => {
                gridOverlay.classList.toggle('hidden');
                // gridButton.classList.toggle('bg-black');
                // gridButton.classList.toggle('bg-red');
                // Save the current state to localStorage
                if (gridOverlay.classList.contains('hidden')) {
                    localStorage.setItem('gridVisible', 'false');
                } else {
                    localStorage.setItem('gridVisible', 'true');
                }
            };

            // Event listener for the button
            gridButton.addEventListener('click', toggleGrid);

            // Check localStorage for grid visibility state and apply it
            const gridVisible = localStorage.getItem('gridVisible');
            if (gridVisible === 'true') {
                gridOverlay.classList.remove('hidden');
            } else {
                gridOverlay.classList.add('hidden');
            }
        }
        gridOverlay();
    });
</script>