<?php
/**
 * Frontend functions.
 *
 * @package twkmedia
 */

if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Custom pagination.
 *
 * @param string  $pages Pages.
 * @param integer $range Range.
 * @return void
 */
function twk_custom_pagination( $pages = '', $range = 2 ) {
	$showitems = ( $range * 2 ) + 1;

	global $paged;
	if ( empty( $paged ) ) {
		$paged = 1;
	}

	if ( '' === $pages ) {
		global $wp_query;

		$pages = $wp_query->max_num_pages;

		if ( ! $pages ) {
			$pages = 1;
		}
	}

	if ( 1 !== $pages ) {
		echo "<div class='pagination'>";
		if ( $paged > 2 && $paged > $range + 1 && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( 1 ) . "'>&laquo;</a>";
		}
		if ( $paged > 1 && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $paged - 1 ) . "'>&lsaquo;</a>";
		}

		for ( $i = 1; $i <= $pages; $i++ ) {
			if ( 1 !== $pages && ( ! ( $i >= $paged + $range + 1 || $i <= $paged - $range - 1 ) || $pages <= $showitems ) ) {
				echo ( $paged === $i ) ? "<span class='current'>" . $i . "</span>" : "<a href='" . get_pagenum_link( $i ) . "' class='inactive' >" . $i . "</a>";
			}
		}

		if ( $paged < $pages && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $paged + 1 ) . "'>&rsaquo;</a>";
		}
		if ( $paged < $pages - 1 && $paged + $range - 1 < $pages && $showitems < $pages ) {
			echo "<a href='" . get_pagenum_link( $pages ) . "'>&raquo;</a>";
		}
		echo "</div>\n";
	}
}


/**
 * Get Yoast primary category link.
 *
 * @return link
 */
function twk_get_primary_cat_link() {
	if ( class_exists( 'WPSEO_Primary_Term' ) ) {
		$wpseo_primary_term = new WPSEO_Primary_Term( 'category', get_the_id() );
		$wpseo_primary_term = $wpseo_primary_term->get_primary_term();
		$term               = get_term( $wpseo_primary_term );

		// Default to first category (not Yoast) if an error is returned.
		if ( ! is_wp_error( $term ) ) {
			$category_display = $term->name;
			$category_link    = get_category_link( $term->term_id );
			return '<a href="' . $category_link . '">' . $category_display . '</a>';
		}
	}
}

/**
 * Get Yoast primary category name.
 *
 * @param string $taxonomy Taxonomy.
 * @return string
 */
function twk_get_primary_cat_name( $taxonomy = 'category' ) {
	if ( class_exists( 'WPSEO_Primary_Term' ) ) {
		$wpseo_primary_term = new WPSEO_Primary_Term( $taxonomy, get_the_id() );
		$wpseo_primary_term = $wpseo_primary_term->get_primary_term();
		$term               = get_term( $wpseo_primary_term );

		if ( ! is_wp_error( $term ) ) {
			return $term->name;
		}
	}

	$terms = get_the_terms( get_the_ID(), $taxonomy );
	if ( $terms && ! is_wp_error( $terms ) ) {
		$term = reset( $terms );
		return $term->name;
	}

	return '';
}


/**
 * It creates a way to have custom lengths excerpts - ex. echo twk_excerpt(32);
 * param $limit integer Number of words to show on the excerpt
 * return $excerpt The excerpt with the limited number of words
 *
 * @param integer $limit Limit of words to show on the excerpt.
 * @param integer $post_id Post ID.
 * @return String $excerpt
 */
function twk_excerpt( $limit = 30, $post_id = null ) {
	if ( ! $post_id ) {
		$post_id = get_the_ID();
	}

	$excerpt = explode( ' ', get_the_excerpt( $post_id ), $limit );

	if ( count( $excerpt ) >= $limit ) {
		array_pop( $excerpt );
		$excerpt = implode( ' ', $excerpt ) . '...';
	} else {
		$excerpt = implode( ' ', $excerpt );
	}

	$excerpt = preg_replace( '`[[^]]*]`', '', $excerpt );

	// If no excerpt we try with the flexible content.
	if ( $excerpt === '' ) {
		$fields        = get_fields( $post_id );
		$first_content = null;

		$blocks              = ( isset( $fields['blocks'] ) ? $fields['blocks'] : null );
		$content_blocks      = array();
		$first_content_block = null;

		if ( $blocks ) {
			foreach ( $blocks as $block ) {
				if ( $block['acf_fc_layout'] === 'text' ) {
					$content_blocks[] = $block;
				}
			}

			if ( isset( $content_blocks[0] ) && isset( $content_blocks[0]['text'] ) ) {
				$first_content_block = $content_blocks[0];
				$first_content       = $first_content_block['text'];
				$first_content       = get_first_paragraph( $first_content );

				$text         = strip_shortcodes( $first_content );
				$text         = apply_filters( 'the_content', $text );
				$text         = str_replace( ']]>', ']]>', $text );
				$excerpt_more = apply_filters( 'excerpt_more', ' ...' );
				$text         = wp_trim_words( $text, $limit, $excerpt_more );

				return $text;
			}

			return '';
		} else {
			return '';
		}
	}

	return $excerpt;
}

/**
 * Gets the first p tag from a string.
 *
 * @param string $string The string to get the first p tag from.
 * @return string
 */
function get_first_paragraph( $string ) {
	$paragraph = substr( $string, 0, strpos( $string, '</p>' ) );

	return $paragraph;
}



/**
 * Creates an anchor tag with the appropriate link and data.
 *
 * @param array  $the_link ACF link field.
 * @param String $classes Classes to be added to the link.
 * @return String HTML
 */
function twk_compose_acf_link( $the_link, $classes = '' ) {
	// Get the data.
	$link_url   = ( $the_link['url'] ) ? $the_link['url'] : '';                // URL.
	$link_title = ( $the_link['title'] ) ? $the_link['title'] : '';            // Title.
	if ( '' === $link_title ) {
		$link_title = get_the_title( url_to_postid( $link_url ) );
	}
	$link_target = ( $the_link['target'] ) ? $the_link['target'] : '_self';    // Target.

	// Scroll to.
	if ( function_exists( 'str_starts_with' ) && str_starts_with( $link_url, '#' ) ) {
		$data_js = 'scroll-to';
	} else {
		$data_js = null;
	}

	// Use the data to create the link (html anchor).
	if ( '_blank' === $link_target ) {
		$return = '<a href="' . esc_url( $link_url ) . '" target="' . esc_attr( $link_target ) . '" rel="noopener noreferrer external" class="' . $classes . '" '. ( $data_js ? 'data-scroll="' . $data_js . '"' : '' ) .'>';
	} else {
		$return = '<a href="' . esc_url( $link_url ) . '" target="' . esc_attr( $link_target ) . '" class="' . $classes . '" '. ( $data_js ? 'data-scroll="' . $data_js . '"' : '' ) .'>';
	}
	if ( strpos( $classes, 'visually-hidden' ) !== false && strpos( $classes, 'visually-hidden' ) >= 0 ) {
		$return .= '<span class="visually-hidden sr-only">' . esc_html( $link_title ) . '</span>';
	} else {
		$return .= esc_html( $link_title );
	}
	$return .= '</a>';

	return $return;
}


/**
 * Returns the video link based on the video provider.
 * 
 * @param String $videoID The video ID.
 * @param String $videoProvider The video provider. Default is 'youtube'.
 * @return String The video link.
 */
function twk_get_video_link( $videoID, $videoProvider = 'youtube' ) {
	if ( $videoProvider === 'youtube' ) {
		return 'https://www.youtube-nocookie.com/watch?v=' . $videoID;
	} elseif ( $videoProvider === 'vimeo' ) {
		return 'https://vimeo.com/' . $videoID . '?dnt=1';
	}
}



/**
 * ACF date field change format
 *
 * @param String $field The ACF field.
 * @param String $format_in The format in which is saved to the database.
 * @param String $format_out The format we want to print out.
 * @return String
 */
function twk_acf_date_format( $field, $format_in = null, $format_out = null ) {
	if ( $format_in === null ) {
		$format_in = 'd/m/Y';
	}

	if ( $format_out === null ) {
		$format_out = 'd-m-Y';
	}

	$date = DateTime::createFromFormat( $format_in, $field );

	return $date->format( $format_out );
}



/**
 * Set the global $post ID.
 *
 * @param integer $post_id The Post ID.
 * @return void
 */
function twk_set_post_id( $post_id ) {
	global $post;
	$post = get_post( $post_id, OBJECT );
	setup_postdata( $post );
}

/**
 * Reset the global $post ID.
 *
 * @return void
 */
function twk_reset_post_id() {
	wp_reset_postdata();
}
