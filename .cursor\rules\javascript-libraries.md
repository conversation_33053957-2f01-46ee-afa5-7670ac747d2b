# JavaScript Libraries Used in This Project

This file lists the JavaScript libraries used in this WordPress theme, primarily managed via npm.

## Core Dependencies (`dependencies`)

These libraries are typically bundled into the main JavaScript files for the frontend.

- **gsap**: `npm:@gsap/shockingly@^3.11.2` - GreenSock Animation Platform (GSAP) for animations.
- **hls.js**: `^1.5.17` - JavaScript HLS client.
- **@fancyapps/ui**: `^6.0.32` - JavaScript library for lightboxes and carousels.
- **set-scrollbar-width**: `^1.0.5` - Utility to set scrollbar width as a CSS variable.
- **tailwind-container-break-out**: `^2.0.6` - Tailwind plugin for container break-out utilities.

## Development Dependencies (`devDependencies`)

These libraries are used during the development and build process (e.g., compiling, linting, bundling).

- **@babel/core**: `^7.13.10` - Babel compiler core.
- **@babel/preset-env**: `^7.13.10` - Babel preset for compiling modern JavaScript down to ES5.
- **@tailwindcss/forms**: `^0.5.7` - Tailwind CSS plugin for form styling.
- **@tailwindcss/typography**: `^0.5.10` - Tailwind CSS plugin for typography styling.
- **babel-loader**: `^8.2.2` - Webpack loader for Babel.
- **basic-ftp**: `^4.6.3` - FTP client library for Node.js.
- **concurrently**: `^8.0.1` - Run multiple commands concurrently.
- **node-notifier**: `^8.0.2` - Cross-platform native notification system.
- **node-watch**: `^0.6.4` - File watcher.
- **npm-run-all**: `^4.1.5` - CLI tool to run multiple npm-scripts in parallel or sequential.
- **ssh2-sftp-client**: `^7.2.1` - SFTP client for Node.js.
- **tailwindcss**: `^3.4.1` - Utility-first CSS framework.
- **tailwindcss-debug-screens**: `^2.2.1` - Tailwind plugin to display the current screen size in development.
- **webpack**: `^5.67.0` - Module bundler.
- **webpack-build-notifier**: `^2.3.0` - Webpack plugin to display build status notifications.
- **webpack-cli**: `^4.9.2` - Command line interface for Webpack.

_Note: This list is generated based on the `package.json` file. Additional libraries might be enqueued directly via WordPress PHP functions (`wp_enqueue_script`) or included via CDN._
