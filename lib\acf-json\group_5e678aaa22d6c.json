{"key": "group_5e678aaa22d6c", "title": "Legal menu", "fields": [{"key": "field_5e678ab507a58", "label": "Legal Menu", "name": "legal_menu", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Link", "hide_collapse": 0, "collapse_all_repeater": 0, "btn-icon-only": 0, "sub_fields": [{"key": "field_5e678ac107a59", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "parent_repeater": "field_5e678ab507a58"}], "rows_per_page": 20, "acfe_repeater_stylised_button": 0}], "location": [[{"param": "options_page", "operator": "==", "value": "acf-options-footer"}]], "menu_order": 1010, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "acfe_display_title": "", "acfe_autosync": ["json"], "acfe_form": 0, "acfe_meta": "", "acfe_note": "", "modified": 1682338186, "acfe_categories": {"base-theme": "Base theme"}}