<?php
/**
 * TWK notifications.
 *
 * @package twkmedia
 */

/**
 * TODO: Add cookie names to WP Rocket exceptions (Advanced Rules - Never Cache Cookies)
 * twk_cookies_popup_
 */

$popup_selected_pages = array();
if ( get_field( 'popup_selected_pages', 'option' ) ) {
	$popup_selected_pages = get_field( 'popup_selected_pages', 'option' );
}
$popup_excluded_pages = array();
if ( get_field( 'popup_exclude_pages', 'option' ) ) {
	$popup_excluded_pages = get_field( 'popup_exclude_pages', 'option' );
}

if ( count( $popup_selected_pages ) > 0 ) {
	if ( in_array( get_the_ID(), $popup_selected_pages ) ) {
		require locate_template( 'templates/sections/notifications/notifications-popup.php' );
	}
} elseif ( ! in_array( get_the_ID(), $popup_excluded_pages ) ) {
	require locate_template( 'templates/sections/notifications/notifications-popup.php' );
}
