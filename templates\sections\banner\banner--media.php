<?php
/**
 * Template part for displaying the page banner media image.
 *
 * @package twkmedia
 */

?>

<section  data-name="banner-media-image" class="h-screen">
	<div class="relative w-full h-full flex flex-col justify-end pb-30">
		<?php echo twk_output_acf_image_with_fallback( get_field( 'banner_image' ), 'full', 'fill-image', 'page', 'eager', true ); ?>
		<div class="overlay overlay--bottom"></div>

		<div class="container relative z-10">
			<h1 class="text-140ncb text-white">
				<?php if ( get_field( 'banner_alternative_title' ) ) : ?>
					<?php echo get_field( 'banner_alternative_title' ); ?>
				<?php else : ?>
					<?php the_title(); ?>
				<?php endif; ?>
			</h1>
		</div>
		<div class="container relative z-10 flex justify-end -mt-40 mix-blend-difference">
			<a href="#main" class="[&_svg]:w-100 [&_svg]:h-auto">
				<?php require locate_template( 'assets/img/icons/scroll-down-arrow.svg' ); ?>
			</a>
		</div>
	</div>
</section>

