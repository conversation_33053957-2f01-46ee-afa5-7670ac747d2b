<?php
/**
 * Load assets
 *
 * @package twkmedia
 */

/**
 * Load CSS and Javascript files
 */
function load_theme_assets() {
	// Register and load CSS.
	wp_enqueue_style( 'screen-css', get_template_directory_uri() . '/assets/css/styles.css', array(), filemtime( get_stylesheet_directory() . '/assets/css/styles.css' ) );

	wp_register_script( 'script-js', get_template_directory_uri() . '/assets/js/dynamic-loader.js', array( 'jquery' ), filemtime( get_stylesheet_directory() . '/assets/js/bundle.js' ), true );
	// PHP variables to pass to JS.
	$php_vars = array(
		'notificationCookieName'   => esc_html( get_field( 'popup_cookies_name', 'option' ) ),
		'notificationCookieExpire' => get_field( 'popup_expiry_time', 'option' ),
		'themeDirUrl'              => get_template_directory_uri(),
		'homeUrl'                  => home_url(),
	);
	wp_localize_script( 'script-js', 'php_vars', $php_vars );
	wp_enqueue_script( 'script-js' );
}
add_action( 'wp_enqueue_scripts', 'load_theme_assets' );


/**
 * Prevent from loading the following styles
 *
 * Gutenberg blocks
 * WooCommerce Gutenberg blocks
 *
 * @return void
 */
function twk_remove_css_styles(){
	wp_dequeue_style( 'wp-block-library' );
	wp_dequeue_style( 'wp-block-library-theme' );
	wp_dequeue_style( 'wc-block-style' ); // Remove WooCommerce block CSS.
}
add_action( 'wp_enqueue_scripts', 'twk_remove_css_styles', 100 );
