function getScrollbarWidth() {
	// Create a temporary container with scroll enabled
	const scrollDiv = document.createElement("div");
	scrollDiv.style.visibility = "hidden";
	scrollDiv.style.overflow = "scroll"; // Forces scrollbar to appear
	scrollDiv.style.msOverflowStyle = "scrollbar"; // For IE 10+
	scrollDiv.style.width = "100px";
	scrollDiv.style.position = "absolute";
	scrollDiv.style.top = "-9999px";

	// Add inner element to get actual width minus scrollbar
	const innerDiv = document.createElement("div");
	innerDiv.style.width = "100%";
	scrollDiv.appendChild(innerDiv);
	document.body.appendChild(scrollDiv);

	// Calculate the scrollbar width
	const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;

	// Clean up
	document.body.removeChild(scrollDiv);

	return scrollbarWidth;
}

function columnBreak(){
	const columnsBreak = document.querySelectorAll( '[data-column-break]' )

	if ( columnsBreak.length > 0 ) {
		columnsBreak.forEach(column => {
			const container               = column.closest('.container')
			const windowWidth             = window.innerWidth
			const containerWidthPx        = window.getComputedStyle(container).maxWidth === 'none' ? windowWidth + 'px' : window.getComputedStyle(container).maxWidth;
			const containerPaddingRightPx = window.getComputedStyle(container).paddingRight;
			const containerPaddingLeftPx  = window.getComputedStyle(container).paddingLeft;
			const containerPaddingRight   = parseInt( containerPaddingRightPx.replace( 'px', '' ) )
			const containerPaddingLeft    = parseInt( containerPaddingLeftPx.replace( 'px', '' ) )
			const containerWidth          = parseInt( containerWidthPx.replace( 'px', '' )  )
			const widthDifference         = windowWidth - containerWidth

			const breakAttribute = column.getAttribute('data-column-break')

			if ( breakAttribute === 'right' ) {
				column.style.marginRight = - Math.round(widthDifference/2 + containerPaddingRight - getScrollbarWidth() / 2) + 'px'
			} else if ( breakAttribute === 'left' ) {
				column.style.marginLeft = - Math.round(widthDifference/2 + containerPaddingLeft - getScrollbarWidth() / 2) + 'px'
			} else if (  breakAttribute === 'both' ) {
				column.style.marginRight = - Math.round(widthDifference/2 + containerPaddingRight - getScrollbarWidth() / 2) + 'px'
				column.style.marginLeft = - Math.round(widthDifference/2 + containerPaddingLeft - getScrollbarWidth() / 2) + 'px'
			}
		});
	}
}

export default function(){
	columnBreak()

	window.addEventListener('resize', columnBreak)
}
