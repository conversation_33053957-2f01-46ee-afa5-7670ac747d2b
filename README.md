# TWK Base Theme

[https://lycee.dev.twkmedia.com/](https://lycee.dev.twkmedia.com/)

- **Project Manager:** <PERSON><PERSON>

- **Designer:** <PERSON><PERSON>

- **Developer:** Aida


## 💾 Bitbucket

[https://bitbucket.org/twkdevelopers/lycee-francais-de-new-york-2025-lfny/](https://bitbucket.org/twkdevelopers/lycee-francais-de-new-york-2025-lfny/)


## 💿 Compiling assets

Boilerplate

### 🧭 Node version
22.11.0

### 👩‍💻👨‍💻 Dev vs Prod mode
This project uses dual development modes to safely work on live sites.

1. Standard Development (`npm run watch`)
- Compiles `styles.css` and `bundle.js`
- Changes affect all users
- Used for normal development

2. Live Site Development mode (`npm run watch:dev-mode`):
- Compiles separate `styles--dev.css` and `bundle--dev.js`
- Changes only visible when dev mode is activated
- Safe for live site development
- No impact on regular user

Dev mode can be activated on the frontend (Top Right)

## 📢 Important
